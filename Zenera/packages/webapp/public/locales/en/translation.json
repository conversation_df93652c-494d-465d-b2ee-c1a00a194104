{"app": {"name": "Zenera", "description": "Modern E-commerce Platform", "welcome": "Welcome to Zenera", "loading": "Loading...", "error": "An error occurred", "success": "Success", "warning": "Warning", "info": "Information"}, "navigation": {"home": "Home", "products": "Products", "categories": "Categories", "cart": "<PERSON><PERSON>", "account": "Account", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "dashboard": "Dashboard", "orders": "Orders", "profile": "Profile", "settings": "Settings", "help": "Help", "contact": "Contact", "about": "About"}, "actions": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "remove": "Remove", "update": "Update", "create": "Create", "submit": "Submit", "reset": "Reset", "clear": "Clear", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "copy": "Copy", "share": "Share", "print": "Print", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "continue": "Continue", "finish": "Finish", "close": "Close", "open": "Open", "expand": "Expand", "collapse": "Collapse", "select": "Select", "deselect": "Deselect", "confirm": "Confirm", "approve": "Approve", "reject": "Reject", "send": "Send", "receive": "Receive"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled", "processing": "Processing", "shipped": "Shipped", "delivered": "Delivered", "returned": "Returned", "refunded": "Refunded", "draft": "Draft", "published": "Published", "archived": "Archived", "deleted": "Deleted"}, "time": {"now": "Now", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This week", "lastWeek": "Last week", "nextWeek": "Next week", "thisMonth": "This month", "lastMonth": "Last month", "nextMonth": "Next month", "thisYear": "This year", "lastYear": "Last year", "nextYear": "Next year", "minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "day": "day", "days": "days", "week": "week", "weeks": "weeks", "month": "month", "months": "months", "year": "year", "years": "years", "ago": "ago", "in": "in"}, "messages": {"noData": "No data available", "noResults": "No results found", "emptyList": "List is empty", "loadingData": "Loading data...", "savingData": "Saving data...", "dataUpdated": "Data updated successfully", "dataSaved": "Data saved successfully", "dataDeleted": "Data deleted successfully", "operationCompleted": "Operation completed successfully", "operationFailed": "Operation failed", "confirmDelete": "Are you sure you want to delete this item?", "confirmAction": "Are you sure you want to perform this action?", "unsavedChanges": "You have unsaved changes. Do you want to save them?", "sessionExpired": "Your session has expired. Please login again.", "accessDenied": "Access denied. You don't have permission to perform this action.", "networkError": "Network error. Please check your connection and try again.", "serverError": "Server error. Please try again later.", "validationError": "Please check the form data and try again.", "fileUploadError": "File upload failed. Please try again.", "fileTooLarge": "File is too large. Maximum size is {{maxSize}}.", "invalidFileType": "Invalid file type. Allowed types: {{allowedTypes}}.", "required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "passwordTooShort": "Password must be at least {{minLength}} characters", "passwordMismatch": "Passwords do not match"}, "language": {"current": "Current language", "select": "Select language", "english": "English", "vietnamese": "Vietnamese", "change": "Change language", "changed": "Language changed to {{language}}"}, "currency": {"vnd": "VND", "usd": "USD", "eur": "EUR", "symbol": {"vnd": "₫", "usd": "$", "eur": "€"}}, "units": {"piece": "piece", "pieces": "pieces", "item": "item", "items": "items", "kg": "kg", "gram": "gram", "liter": "liter", "meter": "meter", "cm": "cm", "mm": "mm"}, "ecommerce": {"product": {"name": "Product Name", "description": "Description", "price": "Price", "comparePrice": "Compare Price", "costPrice": "Cost Price", "category": "Category", "brand": "Brand", "sku": "SKU", "barcode": "Barcode", "stockQuantity": "Stock Quantity", "lowStockThreshold": "Low Stock Threshold", "trackQuantity": "Track Quantity", "inventoryStatus": "Inventory Status", "weight": "Weight", "dimensions": "Dimensions", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "requiresShipping": "Requires Shipping", "images": "Images", "videos": "Videos", "seoTitle": "SEO Title", "seoDescription": "SEO Description", "tags": "Tags", "status": "Status", "featured": "Featured", "digital": "Digital Product"}, "order": {"orderNumber": "Order Number", "status": "Order Status", "paymentStatus": "Payment Status", "customer": "Customer", "shippingAddress": "Shipping Address", "billingAddress": "Billing Address", "paymentMethod": "Payment Method", "shippingMethod": "Shipping Method", "subtotal": "Subtotal", "shippingFee": "Shipping Fee", "discountAmount": "Discount Amount", "totalAmount": "Total Amount", "notes": "Notes", "adminNotes": "Admin Notes"}, "seller": {"personalInfo": "Personal Information", "storeInfo": "Store Information", "businessAddress": "Business Address", "bankInfo": "Bank Information", "documents": "Documents", "agreements": "Terms & Agreements", "storeName": "Store Name", "storeDescription": "Store Description", "businessType": "Business Type", "businessLicense": "Business License", "mainCategory": "Main Category", "accountHolderName": "Account Holder Name", "accountNumber": "Account Number", "bankName": "Bank Name", "branchName": "Branch Name"}, "review": {"rating": "Rating", "title": "Review Title", "content": "Review Content", "images": "Review Images", "anonymous": "Anonymous Review"}, "checkout": {"contactEmail": "Contact Email", "subscribeNewsletter": "Subscribe to Newsletter", "firstName": "First Name", "lastName": "Last Name", "company": "Company", "address1": "Address Line 1", "address2": "Address Line 2", "city": "City", "province": "Province", "postalCode": "Postal Code", "phone": "Phone Number", "billingSameAsShipping": "Billing same as shipping", "orderNotes": "Order Notes"}, "profile": {"displayName": "Display Name", "dateOfBirth": "Date of Birth", "gender": "Gender", "avatar": "Avatar", "preferences": "Preferences", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "marketingEmails": "Marketing Emails", "language": "Language"}}, "pagination": {"page": "Page", "of": "of", "total": "Total", "showing": "Showing", "to": "to", "entries": "entries", "first": "First", "last": "Last", "previous": "Previous", "next": "Next", "itemsPerPage": "Items per page", "goToPage": "Go to page"}, "theme": {"light": "Light", "dark": "Dark", "system": "System", "toggle": "Toggle theme"}}