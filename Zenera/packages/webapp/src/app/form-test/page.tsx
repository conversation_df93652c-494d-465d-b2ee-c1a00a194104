"use client";

import { useState } from "react";
import { useForm } from "@/lib/hooks/use-form";
import { 
  ControlledInput,
  ControlledTextarea,
  ControlledSelect,
  ControlledCheckbox,
  ControlledRadioGroup,
  ControlledFileInput,
  FormSubmitButton,
  FormResetButton,
} from "@/components/forms/form-components";
import { loginSchema, registerSchema, type LoginFormData, type RegisterFormData } from "@/lib/schemas/auth-schemas";
import { productSchema, type ProductFormData } from "@/lib/schemas/product-schemas";
import { MultiStepSellerRegistration } from "@/components/forms/multi-step-form";
import { SchemaForm } from "@/components/forms/schema-form";
import {
  useLoginFormSchema,
  useRegisterFormSchema,
  useProductFormSchema,
  useContactFormSchema
} from "@/lib/schemas/form-schemas";

/**
 * Form Test Page
 * Test page để demo form system với React Hook Form + Zod
 */

export default function FormTestPage() {
  const [activeTab, setActiveTab] = useState<"login" | "register" | "product" | "multistep" | "schema-login" | "schema-product" | "contact">("login");

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg">
          {/* Header */}
          <div className="border-b border-gray-200 px-6 py-4">
            <h1 className="text-2xl font-bold text-gray-900">
              🧪 Form System Test Page
            </h1>
            <p className="text-gray-600 mt-1">
              Test React Hook Form + Zod validation với các form components
            </p>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: "login", label: "Login Form" },
                { id: "register", label: "Register Form" },
                { id: "product", label: "Product Form" },
                { id: "multistep", label: "Multi-Step Form" },
                { id: "schema-login", label: "Schema Login" },
                { id: "schema-product", label: "Schema Product" },
                { id: "contact", label: "Contact Form" },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="p-6">
            {activeTab === "login" && <LoginFormTest />}
            {activeTab === "register" && <RegisterFormTest />}
            {activeTab === "product" && <ProductFormTest />}
            {activeTab === "multistep" && <MultiStepSellerRegistration />}
            {activeTab === "schema-login" && <SchemaLoginFormTest />}
            {activeTab === "schema-product" && <SchemaProductFormTest />}
            {activeTab === "contact" && <ContactFormTest />}
          </div>
        </div>
      </div>
    </div>
  );
}

// Login Form Test
function LoginFormTest() {
  const form = useForm({
    schema: loginSchema,
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
    onSubmit: async (data: LoginFormData) => {
      console.log("Login form submitted:", data);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert("Login thành công!");
    },
    onSuccess: (data) => {
      console.log("Login success:", data);
    },
    onError: (error) => {
      console.error("Login error:", error);
    },
  });

  return (
    <div className="max-w-md mx-auto">
      <h2 className="text-xl font-semibold mb-6">Login Form Test</h2>
      
      <form onSubmit={form.handleSubmit(form.submitForm)} className="space-y-4">
        <ControlledInput
          name="email"
          control={form.control}
          label="Email"
          type="email"
          placeholder="Nhập email của bạn"
          required
        />

        <ControlledInput
          name="password"
          control={form.control}
          label="Mật khẩu"
          type="password"
          placeholder="Nhập mật khẩu"
          required
        />

        <ControlledCheckbox
          name="rememberMe"
          control={form.control}
          label="Ghi nhớ đăng nhập"
        />

        <div className="flex space-x-4 pt-4">
          <FormSubmitButton
            isSubmitting={form.isSubmitting}
            submittingText="Đang đăng nhập..."
            className="flex-1"
          >
            Đăng nhập
          </FormSubmitButton>

          <FormResetButton
            onReset={() => form.reset()}
            disabled={form.isSubmitting}
          />
        </div>
      </form>

      {/* Debug Info */}
      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-medium mb-2">Debug Info:</h3>
        <pre className="text-xs text-gray-600">
          {JSON.stringify({
            values: form.getValues(),
            errors: form.formState.errors,
            isValid: form.formState.isValid,
            isSubmitting: form.isSubmitting,
            submissionState: form.submissionState,
          }, null, 2)}
        </pre>
      </div>
    </div>
  );
}

// Register Form Test
function RegisterFormTest() {
  const form = useForm({
    schema: registerSchema,
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
      firstName: "",
      lastName: "",
      username: "",
      phoneNumber: "",
      role: "customer" as const,
      agreeToTerms: false,
    },
    onSubmit: async (data: RegisterFormData) => {
      console.log("Register form submitted:", data);
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert("Đăng ký thành công!");
    },
  });

  return (
    <div className="max-w-2xl mx-auto">
      <h2 className="text-xl font-semibold mb-6">Register Form Test</h2>
      
      <form onSubmit={form.handleSubmit(form.submitForm)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <ControlledInput
            name="firstName"
            control={form.control}
            label="Tên"
            placeholder="Nhập tên"
            required
          />

          <ControlledInput
            name="lastName"
            control={form.control}
            label="Họ"
            placeholder="Nhập họ"
            required
          />
        </div>

        <ControlledInput
          name="email"
          control={form.control}
          label="Email"
          type="email"
          placeholder="Nhập email"
          required
        />

        <ControlledInput
          name="username"
          control={form.control}
          label="Tên đăng nhập"
          placeholder="Nhập tên đăng nhập (tùy chọn)"
        />

        <ControlledInput
          name="phoneNumber"
          control={form.control}
          label="Số điện thoại"
          type="tel"
          placeholder="Nhập số điện thoại (tùy chọn)"
        />

        <div className="grid grid-cols-2 gap-4">
          <ControlledInput
            name="password"
            control={form.control}
            label="Mật khẩu"
            type="password"
            placeholder="Nhập mật khẩu"
            required
          />

          <ControlledInput
            name="confirmPassword"
            control={form.control}
            label="Xác nhận mật khẩu"
            type="password"
            placeholder="Nhập lại mật khẩu"
            required
          />
        </div>

        <ControlledRadioGroup
          name="role"
          control={form.control}
          label="Loại tài khoản"
          options={[
            { value: "customer", label: "Khách hàng" },
            { value: "seller", label: "Người bán" },
          ]}
          required
        />

        <ControlledCheckbox
          name="agreeToTerms"
          control={form.control}
          label="Tôi đồng ý với điều khoản sử dụng"
          required
        />

        <div className="flex space-x-4 pt-4">
          <FormSubmitButton
            isSubmitting={form.isSubmitting}
            submittingText="Đang đăng ký..."
            className="flex-1"
          >
            Đăng ký
          </FormSubmitButton>

          <FormResetButton
            onReset={() => form.reset()}
            disabled={form.isSubmitting}
          />
        </div>
      </form>
    </div>
  );
}

// Product Form Test
function ProductFormTest() {
  const form = useForm({
    schema: productSchema,
    defaultValues: {
      name: "",
      description: "",
      price: 0,
      inventory: 0,
      categoryId: "",
      status: "draft" as const,
      isDigital: false,
      isFeatured: false,
      requiresShipping: true,
      trackInventory: true,
      allowBackorder: false,
      hasVariants: false,
    },
    onSubmit: async (data: ProductFormData) => {
      console.log("Product form submitted:", data);
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert("Sản phẩm đã được lưu!");
    },
  });

  return (
    <div className="max-w-3xl mx-auto">
      <h2 className="text-xl font-semibold mb-6">Product Form Test</h2>
      
      <form onSubmit={form.handleSubmit(form.submitForm)} className="space-y-6">
        {/* Basic Info */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Thông tin cơ bản</h3>
          
          <ControlledInput
            name="name"
            control={form.control}
            label="Tên sản phẩm"
            placeholder="Nhập tên sản phẩm"
            required
          />

          <ControlledTextarea
            name="description"
            control={form.control}
            label="Mô tả sản phẩm"
            placeholder="Nhập mô tả chi tiết sản phẩm"
            rows={4}
          />

          <div className="grid grid-cols-2 gap-4">
            <ControlledInput
              name="price"
              control={form.control}
              label="Giá bán"
              type="number"
              min="0"
              step="0.01"
              placeholder="0"
              required
            />

            <ControlledInput
              name="inventory"
              control={form.control}
              label="Số lượng tồn kho"
              type="number"
              min="0"
              placeholder="0"
              required
            />
          </div>

          <ControlledSelect
            name="categoryId"
            control={form.control}
            label="Danh mục"
            placeholder="Chọn danh mục sản phẩm"
            options={[
              { value: "electronics", label: "Điện tử" },
              { value: "clothing", label: "Thời trang" },
              { value: "books", label: "Sách" },
              { value: "home", label: "Gia dụng" },
            ]}
            required
          />

          <ControlledSelect
            name="status"
            control={form.control}
            label="Trạng thái"
            options={[
              { value: "draft", label: "Bản nháp" },
              { value: "active", label: "Đang bán" },
              { value: "inactive", label: "Ngừng bán" },
            ]}
            required
          />
        </div>

        {/* Options */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Tùy chọn</h3>
          
          <div className="grid grid-cols-2 gap-4">
            <ControlledCheckbox
              name="isDigital"
              control={form.control}
              label="Sản phẩm số"
            />

            <ControlledCheckbox
              name="isFeatured"
              control={form.control}
              label="Sản phẩm nổi bật"
            />

            <ControlledCheckbox
              name="requiresShipping"
              control={form.control}
              label="Cần vận chuyển"
            />

            <ControlledCheckbox
              name="trackInventory"
              control={form.control}
              label="Theo dõi tồn kho"
            />
          </div>
        </div>

        <div className="flex space-x-4 pt-4">
          <FormSubmitButton
            isSubmitting={form.isSubmitting}
            submittingText="Đang lưu..."
            className="flex-1"
          >
            Lưu sản phẩm
          </FormSubmitButton>

          <FormResetButton
            onReset={() => form.reset()}
            disabled={form.isSubmitting}
          />
        </div>
      </form>
    </div>
  );
}

// Schema Login Form Test
function SchemaLoginFormTest() {
  const loginFormSchema = useLoginFormSchema();

  return (
    <div className="max-w-md mx-auto">
      <h2 className="text-xl font-semibold mb-6">Schema Login Form Test</h2>
      <p className="text-gray-600 mb-6">
        Demo schema-driven form với dynamic generation (inspired by Medoo)
      </p>

      <SchemaForm
        schema={loginFormSchema}
        defaultValues={{
          email: "",
          password: "",
          rememberMe: false,
        }}
        onSubmit={async (data) => {
          console.log("Schema login form submitted:", data);
          await new Promise(resolve => setTimeout(resolve, 2000));
          alert("Schema login thành công!");
        }}
        submitButtonText="Đăng nhập"
        debug={true}
      />
    </div>
  );
}

// Schema Product Form Test
function SchemaProductFormTest() {
  const productFormSchema = useProductFormSchema();

  return (
    <div className="max-w-3xl mx-auto">
      <h2 className="text-xl font-semibold mb-6">Schema Product Form Test</h2>
      <p className="text-gray-600 mb-6">
        Demo advanced schema form với sections và conditional fields
      </p>

      <SchemaForm
        sections={productFormSchema()}
        defaultValues={{
          name: "",
          description: "",
          price: 0,
          inventory: 0,
          categoryId: "",
          status: "draft",
          isDigital: false,
          isFeatured: false,
          requiresShipping: true,
          trackInventory: true,
          allowBackorder: false,
        }}
        onSubmit={async (data) => {
          console.log("Schema product form submitted:", data);
          await new Promise(resolve => setTimeout(resolve, 2000));
          alert("Sản phẩm đã được lưu!");
        }}
        submitButtonText="Lưu sản phẩm"
        layout="vertical"
        debug={true}
      />
    </div>
  );
}

// Contact Form Test
function ContactFormTest() {
  const contactFormSchema = useContactFormSchema();

  return (
    <div className="max-w-2xl mx-auto">
      <h2 className="text-xl font-semibold mb-6">Contact Form Test</h2>
      <p className="text-gray-600 mb-6">
        Demo contact form với validation và error handling
      </p>

      <SchemaForm
        schema={contactFormSchema}
        defaultValues={{
          name: "",
          email: "",
          phone: "",
          subject: "",
          message: "",
        }}
        onSubmit={async (data) => {
          console.log("Contact form submitted:", data);
          await new Promise(resolve => setTimeout(resolve, 2000));
          alert("Tin nhắn đã được gửi!");
        }}
        onSuccess={(response) => {
          console.log("Contact form success:", response);
        }}
        onError={(error) => {
          console.error("Contact form error:", error);
        }}
        submitButtonText="Gửi tin nhắn"
        resetOnSuccess={true}
        withConfirmation={true}
        confirmationMessage="Bạn có chắc chắn muốn gửi tin nhắn này?"
        debug={true}
      />
    </div>
  );
}
