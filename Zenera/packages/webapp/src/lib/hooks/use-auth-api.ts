/**
 * Authentication API Hooks với TanStack Query
 * Provides React hooks for authentication operations
 */

import { 
  useQuery, 
  useMutation, 
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions 
} from '@tanstack/react-query';
import { authApi, LoginRequest, RegisterRequest, AuthResponse, User } from '../api/auth';
import { queryKeys } from './use-api';
import { ApiError } from '../api-client';

// Auth Query Hooks
export const useProfile = (options?: UseQueryOptions<User, ApiError>) => {
  return useQuery({
    queryKey: queryKeys.auth.profile(),
    queryFn: () => authApi.getProfile(),
    enabled: authApi.isAuthenticated(), // Only fetch if authenticated
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on 401 (unauthorized)
      if (error?.statusCode === 401) {
        return false;
      }
      return failureCount < 2;
    },
    ...options,
  });
};

// Auth Mutation Hooks
export const useLogin = (
  options?: UseMutationOptions<AuthResponse, ApiError, LoginRequest>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: authApi.login,
    onSuccess: (data) => {
      // Update profile cache with user data
      queryClient.setQueryData(queryKeys.auth.profile(), data.user);
      
      // Invalidate and refetch profile to ensure fresh data
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.all });
      
      // Call custom success handler if provided
      options?.onSuccess?.(data, {} as LoginRequest, undefined);
    },
    onError: (error) => {
      // Clear any cached auth data on login error
      queryClient.removeQueries({ queryKey: queryKeys.auth.all });
      
      // Call custom error handler if provided
      options?.onError?.(error, {} as LoginRequest, undefined);
    },
    ...options,
  });
};

export const useRegister = (
  options?: UseMutationOptions<AuthResponse, ApiError, RegisterRequest>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: authApi.register,
    onSuccess: (data) => {
      // Update profile cache with user data
      queryClient.setQueryData(queryKeys.auth.profile(), data.user);
      
      // Invalidate and refetch profile
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.all });
      
      // Call custom success handler if provided
      options?.onSuccess?.(data, {} as RegisterRequest, undefined);
    },
    onError: (error) => {
      // Clear any cached auth data on register error
      queryClient.removeQueries({ queryKey: queryKeys.auth.all });
      
      // Call custom error handler if provided
      options?.onError?.(error, {} as RegisterRequest, undefined);
    },
    ...options,
  });
};

export const useLogout = (
  options?: UseMutationOptions<void, ApiError, void>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: authApi.logout,
    onSuccess: () => {
      // Clear all cached data on logout
      queryClient.clear();
      
      // Call custom success handler if provided
      options?.onSuccess?.(undefined, undefined, undefined);
    },
    onError: (error) => {
      // Even on error, clear auth cache (token might be invalid)
      queryClient.removeQueries({ queryKey: queryKeys.auth.all });
      
      // Call custom error handler if provided
      options?.onError?.(error, undefined, undefined);
    },
    ...options,
  });
};

export const useRefreshToken = (
  options?: UseMutationOptions<AuthResponse, ApiError, void>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: authApi.refreshToken,
    onSuccess: (data) => {
      // Update profile cache with fresh user data
      queryClient.setQueryData(queryKeys.auth.profile(), data.user);
      
      // Call custom success handler if provided
      options?.onSuccess?.(data, undefined, undefined);
    },
    onError: (error) => {
      // On refresh error, clear auth data and redirect to login
      queryClient.removeQueries({ queryKey: queryKeys.auth.all });
      
      // Call custom error handler if provided
      options?.onError?.(error, undefined, undefined);
    },
    ...options,
  });
};

// Utility Hooks
export const useAuthStatus = () => {
  const { data: user, isLoading, error } = useProfile();
  
  return {
    user,
    isAuthenticated: !!user && !error,
    isLoading,
    error,
    isGuest: !user && !isLoading,
  };
};

export const useAuthActions = () => {
  const loginMutation = useLogin();
  const registerMutation = useRegister();
  const logoutMutation = useLogout();
  const refreshMutation = useRefreshToken();

  return {
    login: loginMutation.mutate,
    register: registerMutation.mutate,
    logout: logoutMutation.mutate,
    refreshToken: refreshMutation.mutate,
    
    // Loading states
    isLoggingIn: loginMutation.isPending,
    isRegistering: registerMutation.isPending,
    isLoggingOut: logoutMutation.isPending,
    isRefreshing: refreshMutation.isPending,
    
    // Error states
    loginError: loginMutation.error,
    registerError: registerMutation.error,
    logoutError: logoutMutation.error,
    refreshError: refreshMutation.error,
    
    // Reset functions
    resetLoginError: loginMutation.reset,
    resetRegisterError: registerMutation.reset,
    resetLogoutError: logoutMutation.reset,
    resetRefreshError: refreshMutation.reset,
  };
};

// Role-based access hooks
export const useHasRole = (requiredRole: string | string[]) => {
  const { user } = useAuthStatus();
  
  if (!user?.roles) return false;
  
  const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
  return roles.some(role => user.roles.includes(role));
};

export const useIsAdmin = () => {
  return useHasRole(['admin', 'super_admin']);
};

export const useIsSeller = () => {
  return useHasRole('seller');
};

export const useIsCustomer = () => {
  return useHasRole('customer');
};
