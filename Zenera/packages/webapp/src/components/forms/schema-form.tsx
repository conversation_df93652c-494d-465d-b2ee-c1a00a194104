"use client";

import { useEffect, useMemo, useState } from "react";
import { useForm } from "@/lib/hooks/use-form";
import { 
  ControlledInput,
  ControlledTextarea,
  ControlledSelect,
  ControlledCheckbox,
  ControlledRadioGroup,
  ControlledFileInput,
  FormSubmitButton,
  FormResetButton,
} from "./form-components";
import { 
  FormSchemaItem, 
  FormSection, 
  AdvancedFormConfig, 
  SchemaUtils 
} from "@/lib/forms/schema-form";
import { cn } from "@/lib/utils";

/**
 * Schema Form Component
 * Dynamic form generation based on schema (inspired by Medoo's HForm)
 */

interface SchemaFormProps extends AdvancedFormConfig {
  defaultValues?: Record<string, any>;
  className?: string;
}

export function SchemaForm({
  schema,
  sections,
  defaultValues = {},
  endpoint,
  method = 'POST',
  onSubmit,
  onSuccess,
  onError,
  onDataReadyToSubmit,
  resetOnSuccess = true,
  validateOnChange = true,
  validateOnBlur = true,
  layout = 'vertical',
  size = 'medium',
  hideSubmitButton = false,
  hideResetButton = false,
  submitButtonText = 'Gửi',
  resetButtonText = 'Đặt lại',
  withConfirmation = false,
  confirmationMessage = 'Bạn có chắc chắn muốn gửi form này?',
  debug = false,
  className,
  ...config
}: SchemaFormProps) {
  const [mounted, setMounted] = useState(false);
  
  // Resolve schema
  const resolvedSchema = useMemo(() => {
    return SchemaUtils.resolveSchema(schema);
  }, [schema]);

  // Generate validation schema
  const validationSchema = useMemo(() => {
    return SchemaUtils.generateValidationSchema(resolvedSchema);
  }, [resolvedSchema]);

  // Form instance
  const form = useForm({
    schema: validationSchema,
    defaultValues,
    onSubmit: async (data) => {
      if (debug) {
        console.log('Form submitted with data:', data);
      }

      // Transform data if needed
      let transformedData = data;
      if (onDataReadyToSubmit) {
        transformedData = onDataReadyToSubmit(data);
      }

      // Handle confirmation
      if (withConfirmation) {
        const confirmed = window.confirm(confirmationMessage);
        if (!confirmed) return;
      }

      // Call custom submit handler or make API call
      if (onSubmit) {
        return await onSubmit(transformedData);
      } else if (endpoint) {
        // Make API call (implement based on your API client)
        const response = await fetch(endpoint, {
          method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(transformedData),
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
      }
    },
    onSuccess: (data) => {
      if (resetOnSuccess) {
        form.reset();
      }
      onSuccess?.(data);
    },
    onError,
  });

  // Watch form values for conditional rendering
  const formValues = form.watch();

  // Filter visible fields
  const visibleFields = useMemo(() => {
    return SchemaUtils.filterVisibleFields(resolvedSchema, formValues);
  }, [resolvedSchema, formValues]);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div>Loading form...</div>;
  }

  return (
    <div className={cn("schema-form", className)}>
      <form onSubmit={form.handleSubmit(form.submitForm)} className="space-y-6">
        {/* Render sections if provided */}
        {sections ? (
          sections.map((section, sectionIndex) => (
            <FormSection
              key={sectionIndex}
              section={section}
              form={form}
              formValues={formValues}
              size={size}
              debug={debug}
            />
          ))
        ) : (
          /* Render fields directly */
          <div className={cn(
            "grid gap-4",
            layout === 'horizontal' && "grid-cols-2",
            layout === 'inline' && "grid-cols-auto"
          )}>
            {visibleFields.map((field) => (
              <FormFieldRenderer
                key={field.name}
                field={field}
                form={form}
                formValues={formValues}
                size={size}
                debug={debug}
              />
            ))}
          </div>
        )}

        {/* Form Actions */}
        {(!hideSubmitButton || !hideResetButton) && (
          <div className="flex space-x-4 pt-4">
            {!hideSubmitButton && (
              <FormSubmitButton
                isSubmitting={form.isSubmitting}
                submittingText="Đang xử lý..."
                className="flex-1"
                size={size}
              >
                {submitButtonText}
              </FormSubmitButton>
            )}

            {!hideResetButton && (
              <FormResetButton
                onReset={() => form.reset()}
                disabled={form.isSubmitting}
                size={size}
              >
                {resetButtonText}
              </FormResetButton>
            )}
          </div>
        )}

        {/* Debug Info */}
        {debug && (
          <div className="mt-8 p-4 bg-gray-100 rounded-lg">
            <h3 className="font-medium mb-2">Debug Info:</h3>
            <pre className="text-xs text-gray-600">
              {JSON.stringify({
                values: form.getValues(),
                errors: form.formState.errors,
                isValid: form.formState.isValid,
                isSubmitting: form.isSubmitting,
                visibleFields: visibleFields.map(f => f.name),
              }, null, 2)}
            </pre>
          </div>
        )}
      </form>
    </div>
  );
}

// Form Section Component
interface FormSectionProps {
  section: FormSection;
  form: any;
  formValues: any;
  size: string;
  debug: boolean;
}

function FormSection({ section, form, formValues, size, debug }: FormSectionProps) {
  const [collapsed, setCollapsed] = useState(section.defaultCollapsed || false);

  // Check if section should be visible
  const isVisible = useMemo(() => {
    if (section.when) {
      const { field, condition } = section.when;
      const dependentValue = formValues[field];
      return condition(dependentValue);
    }
    return true;
  }, [section.when, formValues]);

  if (!isVisible) return null;

  const visibleFields = SchemaUtils.filterVisibleFields(section.fields, formValues);

  return (
    <div className={cn("form-section", section.className)}>
      {/* Section Header */}
      {(section.title || section.description) && (
        <div className="mb-4">
          {section.title && (
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                {section.title}
              </h3>
              {section.collapsible && (
                <button
                  type="button"
                  onClick={() => setCollapsed(!collapsed)}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  {collapsed ? 'Mở rộng' : 'Thu gọn'}
                </button>
              )}
            </div>
          )}
          {section.description && (
            <p className="text-sm text-gray-600 mt-1">
              {section.description}
            </p>
          )}
        </div>
      )}

      {/* Section Fields */}
      {!collapsed && (
        <div className="grid gap-4">
          {visibleFields.map((field) => (
            <FormFieldRenderer
              key={field.name}
              field={field}
              form={form}
              formValues={formValues}
              size={size}
              debug={debug}
            />
          ))}
        </div>
      )}
    </div>
  );
}

// Form Field Renderer
interface FormFieldRendererProps {
  field: FormSchemaItem;
  form: any;
  formValues: any;
  size: string;
  debug: boolean;
}

function FormFieldRenderer({ field, form, formValues, size, debug }: FormFieldRendererProps) {
  // Handle custom components
  if (field.Component) {
    const CustomComponent = field.Component;
    return (
      <CustomComponent
        name={field.name}
        control={form.control}
        field={field}
        formValues={formValues}
        {...field.componentProps}
      />
    );
  }

  // Common props
  const commonProps = {
    name: field.name,
    control: form.control,
    label: field.label,
    hint: field.hint,
    required: field.required,
    disabled: field.disabled,
    className: field.className,
    ...field.componentProps,
  };

  // Render based on field type
  switch (field.type) {
    case 'input':
    case 'email':
    case 'password':
    case 'phone':
    case 'url':
    case 'search':
    case 'color':
      return (
        <ControlledInput
          {...commonProps}
          type={field.type === 'input' ? 'text' : field.type}
          placeholder={field.placeholder}
          maxLength={field.maxLength}
        />
      );

    case 'number':
    case 'range':
      return (
        <ControlledInput
          {...commonProps}
          type={field.type}
          placeholder={field.placeholder}
          min={field.min}
          max={field.max}
          step={field.step}
        />
      );

    case 'textarea':
      return (
        <ControlledTextarea
          {...commonProps}
          placeholder={field.placeholder}
          maxLength={field.maxLength}
        />
      );

    case 'select':
      return (
        <ControlledSelect
          {...commonProps}
          placeholder={field.placeholder}
          options={field.options || []}
        />
      );

    case 'checkbox':
      return (
        <ControlledCheckbox
          {...commonProps}
        />
      );

    case 'radio':
      return (
        <ControlledRadioGroup
          {...commonProps}
          options={field.options || []}
        />
      );

    case 'file':
      return (
        <ControlledFileInput
          {...commonProps}
          accept={field.accept}
          multiple={field.multiple}
        />
      );

    case 'date':
    case 'datetime-local':
    case 'time':
    case 'month':
    case 'week':
      return (
        <ControlledInput
          {...commonProps}
          type={field.type}
        />
      );

    case 'hidden':
      return (
        <input
          type="hidden"
          {...form.register(field.name)}
        />
      );

    default:
      if (debug) {
        console.warn(`Unknown field type: ${field.type}`);
      }
      return (
        <ControlledInput
          {...commonProps}
          type="text"
          placeholder={field.placeholder}
        />
      );
  }
}
