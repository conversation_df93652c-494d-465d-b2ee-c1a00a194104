import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

async function bootstrap() {
  console.log('🚀 Starting Zenera API Server...');

  try {
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    // Enable API versioning
    app.enableVersioning({
      type: VersioningType.URI,
      defaultVersion: '1',
    });

    // Global pipes
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
      }),
    );

    // Swagger setup
    const config = new DocumentBuilder()
      .setTitle('Zenera E-commerce API')
      .setDescription('The Zenera E-commerce Platform API')
      .setVersion('1.0')
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api', app, document);

    // CORS - Support multiple development ports
    app.enableCors({
      origin: [
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:3002',
        'http://localhost:3003',
        'http://localhost:3004',
        'http://localhost:3005',
        'http://localhost:3006',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:3001',
        'http://127.0.0.1:3002',
        'http://127.0.0.1:3003',
        'http://127.0.0.1:3004',
        'http://127.0.0.1:3005',
        'http://127.0.0.1:3006',
      ],
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
      credentials: true,
    });

    // Start server with dynamic port allocation
    const port = process.env.PORT || 3001;

    try {
      await app.listen(port);
      console.log(`✅ Zenera API Server running on: http://localhost:${port}`);
      console.log(`📚 Swagger documentation: http://localhost:${port}/api`);
    } catch (error: any) {
      if (error.code === 'EADDRINUSE') {
        console.log(`⚠️ Port ${port} is busy, trying alternative ports...`);

        // Try alternative ports
        const alternativePorts = [3002, 3003, 3004, 3005, 3006];
        let serverStarted = false;

        for (const altPort of alternativePorts) {
          try {
            await app.listen(altPort);
            console.log(`✅ Zenera API Server running on: http://localhost:${altPort}`);
            console.log(`📚 Swagger documentation: http://localhost:${altPort}/api`);
            console.log(`💡 Note: Update your frontend API configuration to use port ${altPort}`);
            serverStarted = true;
            break;
          } catch (altError: any) {
            if (altError.code === 'EADDRINUSE') {
              console.log(`⚠️ Port ${altPort} is also busy, trying next...`);
              continue;
            } else {
              throw altError;
            }
          }
        }

        if (!serverStarted) {
          throw new Error('All alternative ports are busy. Please free up a port or specify a custom PORT environment variable.');
        }
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error('❌ Error starting Zenera API Server:', error);
    process.exit(1);
  }
}

bootstrap().catch(err => {
  console.error('💥 Fatal error during bootstrap:', err);
  process.exit(1);
});
