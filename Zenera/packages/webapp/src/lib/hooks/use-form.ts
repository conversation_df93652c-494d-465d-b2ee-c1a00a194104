"use client";

import { useState } from "react";
import { useForm as useReactHookForm, UseFormProps, UseFormReturn } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { FormSubmissionState, FormSubmissionResult, handleFormSubmission } from "../forms/form-utils";

/**
 * Enhanced useForm hook với Zod validation và submission handling
 */
export interface UseFormOptions<T extends z.ZodType> extends Omit<UseFormProps<z.infer<T>>, 'resolver'> {
  schema: T;
  onSubmit?: (data: z.infer<T>) => Promise<any>;
  onSubmitAndContinue?: (data: z.infer<T>) => Promise<any>;
  onSuccess?: (data: any) => void;
  onSuccessAndContinue?: (data: any) => void;
  onError?: (error: string) => void;
  resetIfSuccess?: boolean;
}

export interface UseFormResult<T extends z.ZodType> extends UseFormReturn<z.infer<T>> {
  submissionState: FormSubmissionState;
  submitForm: () => Promise<void>;
  submitAndContinueForm: () => Promise<void>;
  isSubmitting: boolean;
  isSubmittingAndContinue: boolean;
  submitError: string | null;
}

export function useForm<T extends z.ZodType>({
  schema,
  onSubmit,
  onSubmitAndContinue,
  onSuccess,
  onSuccessAndContinue,
  onError,
  resetIfSuccess = true,
  ...options
}: UseFormOptions<T>): UseFormResult<T> {
  const [submissionState, setSubmissionState] = useState<FormSubmissionState>('idle');
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSubmittingAndContinue, setIsSubmittingAndContinue] = useState(false);

  const form = useReactHookForm<z.infer<T>>({
    resolver: zodResolver(schema),
    ...options,
  });

  const submitForm = async () => {
    if (!onSubmit) return;

    const isValid = await form.trigger();
    if (!isValid) return;

    setSubmissionState('submitting');
    setSubmitError(null);

    const result = await handleFormSubmission(
      () => onSubmit(form.getValues()),
      (data) => {
        setSubmissionState('success');
        if (resetIfSuccess) {
          form.reset();
        }
        onSuccess?.(data);
      },
      (error) => {
        setSubmissionState('error');
        setSubmitError(error);
        onError?.(error);
      }
    );

    if (!result.success && result.fieldErrors) {
      // Set field-specific errors
      Object.entries(result.fieldErrors).forEach(([field, message]) => {
        form.setError(field as any, { message });
      });
    }
  };

  const submitAndContinueForm = async () => {
    if (!onSubmitAndContinue) return;

    const isValid = await form.trigger();
    if (!isValid) return;

    setIsSubmittingAndContinue(true);
    setSubmissionState('submitting');
    setSubmitError(null);

    const result = await handleFormSubmission(
      () => onSubmitAndContinue(form.getValues()),
      (data) => {
        setSubmissionState('success');
        // Don't reset form for submit and continue
        onSuccessAndContinue?.(data);
      },
      (error) => {
        setSubmissionState('error');
        setSubmitError(error);
        onError?.(error);
      }
    );

    if (!result.success && result.fieldErrors) {
      // Set field-specific errors
      Object.entries(result.fieldErrors).forEach(([field, message]) => {
        form.setError(field as any, { message });
      });
    }

    setIsSubmittingAndContinue(false);
  };

  return {
    ...form,
    submissionState,
    submitForm,
    submitAndContinueForm,
    isSubmitting: submissionState === 'submitting' && !isSubmittingAndContinue,
    isSubmittingAndContinue,
    submitError,
  };
}

/**
 * Hook cho form validation với debounce
 */
export function useFormValidation<T extends z.ZodType>(
  schema: T,
  debounceMs: number = 300
) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidating, setIsValidating] = useState(false);

  const validate = async (data: Partial<z.infer<T>>) => {
    setIsValidating(true);
    
    try {
      await schema.parseAsync(data);
      setErrors({});
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path.join('.');
          fieldErrors[path] = err.message;
        });
        setErrors(fieldErrors);
      }
    } finally {
      setIsValidating(false);
    }
  };

  return {
    errors,
    isValidating,
    validate,
  };
}

/**
 * Hook cho multi-step forms
 */
export interface UseMultiStepFormOptions<T extends z.ZodType> {
  steps: Array<{
    name: string;
    schema: z.ZodType;
    fields: string[];
  }>;
  finalSchema: T;
  onSubmit?: (data: z.infer<T>) => Promise<any>;
}

export function useMultiStepForm<T extends z.ZodType>({
  steps,
  finalSchema,
  onSubmit,
}: UseMultiStepFormOptions<T>) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const form = useForm({
    schema: finalSchema,
    onSubmit,
  });

  const currentStepConfig = steps[currentStep];
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === steps.length - 1;
  const canGoNext = completedSteps.includes(currentStep);

  const validateCurrentStep = async () => {
    if (!currentStepConfig) return false;

    const currentData = form.getValues();
    const stepData: Record<string, any> = {};
    
    currentStepConfig.fields.forEach((field) => {
      stepData[field] = currentData[field];
    });

    try {
      await currentStepConfig.schema.parseAsync(stepData);
      
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps([...completedSteps, currentStep]);
      }
      
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        error.errors.forEach((err) => {
          const path = err.path.join('.');
          form.setError(path as any, { message: err.message });
        });
      }
      return false;
    }
  };

  const goToNext = async () => {
    const isValid = await validateCurrentStep();
    if (isValid && !isLastStep) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPrevious = () => {
    if (!isFirstStep) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 0 && step < steps.length) {
      setCurrentStep(step);
    }
  };

  const submitForm = async () => {
    const isValid = await validateCurrentStep();
    if (isValid && isLastStep) {
      await form.submitForm();
    }
  };

  return {
    ...form,
    currentStep,
    currentStepConfig,
    isFirstStep,
    isLastStep,
    canGoNext,
    completedSteps,
    totalSteps: steps.length,
    goToNext,
    goToPrevious,
    goToStep,
    validateCurrentStep,
    submitForm,
  };
}

/**
 * Hook cho form auto-save
 */
export interface UseAutoSaveOptions<T> {
  onSave: (data: T) => Promise<void>;
  debounceMs?: number;
  enabled?: boolean;
}

export function useAutoSave<T>({
  onSave,
  debounceMs = 1000,
  enabled = true,
}: UseAutoSaveOptions<T>) {
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [saveError, setSaveError] = useState<string | null>(null);

  const save = async (data: T) => {
    if (!enabled) return;

    setIsSaving(true);
    setSaveError(null);

    try {
      await onSave(data);
      setLastSaved(new Date());
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Lỗi lưu tự động';
      setSaveError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  return {
    save,
    isSaving,
    lastSaved,
    saveError,
  };
}
