/**
 * Order Form Schemas
 * Zod schemas cho order management forms
 */

import { z } from "zod";
import { commonValidations } from "../forms/form-utils";
import { addressSchema } from "./auth-schemas";

/**
 * Order Item Schema
 */
export const orderItemSchema = z.object({
  productId: commonValidations.required("ID sản phẩm là bắt buộc"),
  variantId: z.string().optional(),
  quantity: commonValidations.positiveNumber,
  price: commonValidations.price,
  originalPrice: commonValidations.price.optional(),
  discount: z.number().min(0).optional(),
  total: commonValidations.price,
  productName: commonValidations.required("Tên sản phẩm là bắt buộc"),
  productImage: z.string().optional(),
  productSku: z.string().optional(),
  variantName: z.string().optional(),
  attributes: z.record(z.string()).optional(),
});

export type OrderItemFormData = z.infer<typeof orderItemSchema>;

/**
 * Shipping Info Schema
 */
export const shippingInfoSchema = z.object({
  method: z.enum(["standard", "express", "overnight"], {
    required_error: "Vui lòng chọn phương thức vận chuyển",
  }),
  cost: commonValidations.price,
  estimatedDays: z.number().min(1),
  trackingNumber: z.string().optional(),
  carrier: z.string().optional(),
  notes: z.string().optional(),
});

export type ShippingInfoFormData = z.infer<typeof shippingInfoSchema>;

/**
 * Payment Info Schema
 */
export const paymentInfoSchema = z.object({
  method: z.enum(["credit_card", "debit_card", "paypal", "bank_transfer", "cod"], {
    required_error: "Vui lòng chọn phương thức thanh toán",
  }),
  status: z.enum(["pending", "processing", "completed", "failed", "refunded"], {
    required_error: "Vui lòng chọn trạng thái thanh toán",
  }),
  transactionId: z.string().optional(),
  amount: commonValidations.price,
  currency: z.string().default("VND"),
  paidAt: z.date().optional(),
  refundedAt: z.date().optional(),
  refundAmount: z.number().min(0).optional(),
  notes: z.string().optional(),
});

export type PaymentInfoFormData = z.infer<typeof paymentInfoSchema>;

/**
 * Order Schema
 */
export const orderSchema = z.object({
  // Customer Info
  customerId: z.string().optional(),
  customerEmail: commonValidations.email,
  customerPhone: commonValidations.phone.optional(),
  customerName: commonValidations.required("Tên khách hàng là bắt buộc"),
  
  // Order Items
  items: z.array(orderItemSchema).min(1, "Đơn hàng phải có ít nhất một sản phẩm"),
  
  // Addresses
  billingAddress: addressSchema,
  shippingAddress: addressSchema,
  sameAsShipping: z.boolean().default(true),
  
  // Pricing
  subtotal: commonValidations.price,
  taxAmount: z.number().min(0).default(0),
  shippingCost: z.number().min(0).default(0),
  discountAmount: z.number().min(0).default(0),
  total: commonValidations.price,
  
  // Shipping
  shipping: shippingInfoSchema.optional(),
  
  // Payment
  payment: paymentInfoSchema,
  
  // Status
  status: z.enum([
    "pending",
    "confirmed",
    "processing",
    "shipped",
    "delivered",
    "cancelled",
    "refunded"
  ], {
    required_error: "Vui lòng chọn trạng thái đơn hàng",
  }),
  
  // Additional Info
  notes: z.string().optional(),
  internalNotes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  
  // Dates
  orderDate: z.date().default(() => new Date()),
  shippedDate: z.date().optional(),
  deliveredDate: z.date().optional(),
  
  // Discounts & Coupons
  couponCode: z.string().optional(),
  discountType: z.enum(["percentage", "fixed"]).optional(),
  discountValue: z.number().min(0).optional(),
});

export type OrderFormData = z.infer<typeof orderSchema>;

/**
 * Order Status Update Schema
 */
export const orderStatusUpdateSchema = z.object({
  orderId: commonValidations.required("ID đơn hàng là bắt buộc"),
  status: z.enum([
    "pending",
    "confirmed",
    "processing",
    "shipped",
    "delivered",
    "cancelled",
    "refunded"
  ], {
    required_error: "Vui lòng chọn trạng thái đơn hàng",
  }),
  notes: z.string().optional(),
  notifyCustomer: z.boolean().default(true),
  trackingNumber: z.string().optional(),
});

export type OrderStatusUpdateFormData = z.infer<typeof orderStatusUpdateSchema>;

/**
 * Order Refund Schema
 */
export const orderRefundSchema = z.object({
  orderId: commonValidations.required("ID đơn hàng là bắt buộc"),
  amount: commonValidations.price,
  reason: z.enum([
    "customer_request",
    "defective_product",
    "wrong_item",
    "damaged_shipping",
    "other"
  ], {
    required_error: "Vui lòng chọn lý do hoàn tiền",
  }),
  reasonText: z.string().optional(),
  refundMethod: z.enum(["original_payment", "store_credit", "bank_transfer"], {
    required_error: "Vui lòng chọn phương thức hoàn tiền",
  }),
  items: z.array(z.object({
    orderItemId: z.string(),
    quantity: commonValidations.positiveNumber,
    amount: commonValidations.price,
  })).optional(),
  restockItems: z.boolean().default(true),
  notifyCustomer: z.boolean().default(true),
  notes: z.string().optional(),
});

export type OrderRefundFormData = z.infer<typeof orderRefundSchema>;

/**
 * Order Search Schema
 */
export const orderSearchSchema = z.object({
  query: z.string().optional(),
  status: z.array(z.string()).optional(),
  paymentStatus: z.array(z.string()).optional(),
  customerId: z.string().optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  minAmount: z.number().min(0).optional(),
  maxAmount: z.number().min(0).optional(),
  shippingMethod: z.string().optional(),
  paymentMethod: z.string().optional(),
  sortBy: z.enum([
    "order_date",
    "total",
    "status",
    "customer_name"
  ]).optional(),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

export type OrderSearchFormData = z.infer<typeof orderSearchSchema>;

/**
 * Checkout Schema
 */
export const checkoutSchema = z.object({
  // Cart Items
  items: z.array(z.object({
    productId: z.string(),
    variantId: z.string().optional(),
    quantity: commonValidations.positiveNumber,
  })).min(1, "Giỏ hàng không được trống"),
  
  // Customer Info
  email: commonValidations.email,
  phone: commonValidations.phone.optional(),
  firstName: commonValidations.required("Tên là bắt buộc"),
  lastName: commonValidations.required("Họ là bắt buộc"),
  
  // Addresses
  billingAddress: addressSchema,
  shippingAddress: addressSchema,
  sameAsShipping: z.boolean().default(true),
  
  // Shipping & Payment
  shippingMethod: z.string({
    required_error: "Vui lòng chọn phương thức vận chuyển",
  }),
  paymentMethod: z.string({
    required_error: "Vui lòng chọn phương thức thanh toán",
  }),
  
  // Discounts
  couponCode: z.string().optional(),
  
  // Additional
  notes: z.string().optional(),
  agreeToTerms: z.boolean().refine((val) => val === true, {
    message: "Bạn phải đồng ý với điều khoản mua hàng",
  }),
  subscribeNewsletter: z.boolean().default(false),
});

export type CheckoutFormData = z.infer<typeof checkoutSchema>;

/**
 * Cart Item Schema
 */
export const cartItemSchema = z.object({
  productId: commonValidations.required("ID sản phẩm là bắt buộc"),
  variantId: z.string().optional(),
  quantity: commonValidations.positiveNumber,
});

export type CartItemFormData = z.infer<typeof cartItemSchema>;

/**
 * Coupon Schema
 */
export const couponSchema = z.object({
  code: z.string()
    .min(3, "Mã giảm giá phải có ít nhất 3 ký tự")
    .max(20, "Mã giảm giá không được quá 20 ký tự")
    .regex(/^[A-Z0-9]+$/, "Mã giảm giá chỉ được chứa chữ hoa và số"),
  name: commonValidations.required("Tên coupon là bắt buộc"),
  description: z.string().optional(),
  type: z.enum(["percentage", "fixed"], {
    required_error: "Vui lòng chọn loại giảm giá",
  }),
  value: commonValidations.positiveNumber,
  minimumAmount: z.number().min(0).optional(),
  maximumDiscount: z.number().min(0).optional(),
  usageLimit: z.number().min(1).optional(),
  usagePerCustomer: z.number().min(1).optional(),
  startDate: z.date(),
  endDate: z.date(),
  isActive: z.boolean().default(true),
  applicableProducts: z.array(z.string()).optional(),
  applicableCategories: z.array(z.string()).optional(),
}).refine((data) => data.endDate > data.startDate, {
  message: "Ngày kết thúc phải sau ngày bắt đầu",
  path: ["endDate"],
});

export type CouponFormData = z.infer<typeof couponSchema>;
