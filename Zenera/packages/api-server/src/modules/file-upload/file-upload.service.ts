import { Injectable, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as multer from 'multer';
import * as path from 'path';
import * as fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
  url: string;
}

@Injectable()
export class FileUploadService {
  private readonly uploadPath: string;
  private readonly maxFileSize: number;
  private readonly allowedMimeTypes: string[];

  constructor(private configService: ConfigService) {
    this.uploadPath = this.configService.get('UPLOAD_PATH', './uploads');
    this.maxFileSize = this.configService.get('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
    this.allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];

    // Ensure upload directory exists
    this.ensureUploadDirectory();
  }

  private ensureUploadDirectory(): void {
    if (!fs.existsSync(this.uploadPath)) {
      fs.mkdirSync(this.uploadPath, { recursive: true });
    }

    // Create subdirectories
    const subdirs = ['images', 'documents', 'products', 'avatars'];
    subdirs.forEach(subdir => {
      const subdirPath = path.join(this.uploadPath, subdir);
      if (!fs.existsSync(subdirPath)) {
        fs.mkdirSync(subdirPath, { recursive: true });
      }
    });
  }

  /**
   * Get multer configuration for file uploads
   */
  getMulterConfig(subfolder: string = 'general'): multer.Options {
    const storage = multer.diskStorage({
      destination: (req, file, cb) => {
        const uploadDir = path.join(this.uploadPath, subfolder);
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
      },
      filename: (req, file, cb) => {
        const uniqueSuffix = `${Date.now()}-${uuidv4()}`;
        const ext = path.extname(file.originalname);
        const name = path.basename(file.originalname, ext);
        const sanitizedName = name.replace(/[^a-zA-Z0-9]/g, '_');
        cb(null, `${sanitizedName}-${uniqueSuffix}${ext}`);
      },
    });

    return {
      storage,
      limits: {
        fileSize: this.maxFileSize,
        files: 10, // Maximum 10 files per request
      },
      fileFilter: (req, file, cb) => {
        if (this.allowedMimeTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new BadRequestException(`File type ${file.mimetype} is not allowed`), false);
        }
      },
    };
  }

  /**
   * Process uploaded files and return file information
   */
  processUploadedFiles(files: Express.Multer.File[], baseUrl: string): UploadedFile[] {
    return files.map(file => ({
      fieldname: file.fieldname,
      originalname: file.originalname,
      encoding: file.encoding,
      mimetype: file.mimetype,
      size: file.size,
      destination: file.destination,
      filename: file.filename,
      path: file.path,
      url: `${baseUrl}/uploads/${path.basename(file.destination)}/${file.filename}`,
    }));
  }

  /**
   * Upload single file
   */
  async uploadSingleFile(file: Express.Multer.File, subfolder: string = 'general'): Promise<UploadedFile> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    try {
      const baseUrl = this.configService.get('BASE_URL', 'http://localhost:3001');
      const processedFiles = this.processUploadedFiles([file], baseUrl);
      return processedFiles[0];
    } catch (error) {
      throw new InternalServerErrorException('Failed to process uploaded file');
    }
  }

  /**
   * Upload multiple files
   */
  async uploadMultipleFiles(files: Express.Multer.File[], subfolder: string = 'general'): Promise<UploadedFile[]> {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files provided');
    }

    try {
      const baseUrl = this.configService.get('BASE_URL', 'http://localhost:3001');
      return this.processUploadedFiles(files, baseUrl);
    } catch (error) {
      throw new InternalServerErrorException('Failed to process uploaded files');
    }
  }

  /**
   * Delete file
   */
  async deleteFile(filename: string, subfolder: string = 'general'): Promise<void> {
    const filePath = path.join(this.uploadPath, subfolder, filename);
    
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      throw new InternalServerErrorException('Failed to delete file');
    }
  }

  /**
   * Get file info
   */
  async getFileInfo(filename: string, subfolder: string = 'general'): Promise<any> {
    const filePath = path.join(this.uploadPath, subfolder, filename);
    
    try {
      if (!fs.existsSync(filePath)) {
        throw new BadRequestException('File not found');
      }

      const stats = fs.statSync(filePath);
      const baseUrl = this.configService.get('BASE_URL', 'http://localhost:3001');
      
      return {
        filename,
        size: stats.size,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        url: `${baseUrl}/uploads/${subfolder}/${filename}`,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get file info');
    }
  }

  /**
   * Validate file type
   */
  validateFileType(mimetype: string): boolean {
    return this.allowedMimeTypes.includes(mimetype);
  }

  /**
   * Validate file size
   */
  validateFileSize(size: number): boolean {
    return size <= this.maxFileSize;
  }

  /**
   * Get upload statistics
   */
  async getUploadStats(): Promise<any> {
    try {
      const stats = {
        totalFiles: 0,
        totalSize: 0,
        byType: {} as Record<string, number>,
        byFolder: {} as Record<string, number>,
      };

      const scanDirectory = (dirPath: string, folderName: string) => {
        if (!fs.existsSync(dirPath)) return;

        const files = fs.readdirSync(dirPath);
        stats.byFolder[folderName] = files.length;

        files.forEach(file => {
          const filePath = path.join(dirPath, file);
          const fileStat = fs.statSync(filePath);

          if (fileStat.isFile()) {
            stats.totalFiles++;
            stats.totalSize += fileStat.size;

            const ext = path.extname(file).toLowerCase();
            stats.byType[ext] = (stats.byType[ext] || 0) + 1;
          }
        });
      };

      // Scan main upload directory
      scanDirectory(this.uploadPath, 'root');

      // Scan subdirectories
      const subdirs = ['images', 'documents', 'products', 'avatars'];
      subdirs.forEach(subdir => {
        scanDirectory(path.join(this.uploadPath, subdir), subdir);
      });

      return stats;
    } catch (error) {
      throw new InternalServerErrorException('Failed to get upload statistics');
    }
  }

  /**
   * Clean up old files (older than specified days)
   */
  async cleanupOldFiles(daysOld: number = 30): Promise<{ deletedFiles: number; freedSpace: number }> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    let deletedFiles = 0;
    let freedSpace = 0;

    const cleanDirectory = (dirPath: string) => {
      if (!fs.existsSync(dirPath)) return;

      const files = fs.readdirSync(dirPath);
      files.forEach(file => {
        const filePath = path.join(dirPath, file);
        const fileStat = fs.statSync(filePath);

        if (fileStat.isFile() && fileStat.mtime < cutoffDate) {
          try {
            freedSpace += fileStat.size;
            fs.unlinkSync(filePath);
            deletedFiles++;
          } catch (error) {
            console.error(`Failed to delete file ${filePath}:`, error);
          }
        }
      });
    };

    // Clean main directory and subdirectories
    cleanDirectory(this.uploadPath);
    const subdirs = ['images', 'documents', 'products', 'avatars'];
    subdirs.forEach(subdir => {
      cleanDirectory(path.join(this.uploadPath, subdir));
    });

    return { deletedFiles, freedSpace };
  }
}
