"use client";

import { useState } from "react";
import { useMultiStepForm } from "@/lib/hooks/use-form";
import { 
  ControlledInput,
  ControlledTextarea,
  ControlledSelect,
  ControlledCheckbox,
  FormSubmitButton,
} from "./form-components";
import { sellerRegistrationSchema, type SellerRegistrationFormData } from "@/lib/schemas/auth-schemas";
import { z } from "zod";
import { cn } from "@/lib/utils";

/**
 * Multi-Step Form Example
 * Demo multi-step form với seller registration
 */

// Step schemas
const personalInfoSchema = z.object({
  firstName: z.string().min(1, "Tên là bắt buộc"),
  lastName: z.string().min(1, "Họ là bắt buộc"),
  email: z.string().email("Email không hợp lệ"),
  phoneNumber: z.string().min(1, "<PERSON><PERSON> điện thoại là bắt buộc"),
});

const businessInfoSchema = z.object({
  businessName: z.string().min(1, "Tên doanh nghiệp là bắt buộc"),
  businessType: z.enum(["individual", "company"]),
  taxId: z.string().optional(),
});

const storeInfoSchema = z.object({
  storeName: z.string().min(1, "Tên cửa hàng là bắt buộc"),
  storeDescription: z.string().optional(),
  storeCategory: z.string().min(1, "Danh mục cửa hàng là bắt buộc"),
});

const addressInfoSchema = z.object({
  address: z.object({
    name: z.string().min(1, "Tên địa chỉ là bắt buộc"),
    phone: z.string().min(1, "Số điện thoại là bắt buộc"),
    address: z.string().min(1, "Địa chỉ là bắt buộc"),
    ward: z.string().min(1, "Phường/Xã là bắt buộc"),
    district: z.string().min(1, "Quận/Huyện là bắt buộc"),
    province: z.string().min(1, "Tỉnh/Thành phố là bắt buộc"),
    type: z.enum(["home", "work", "other"]),
    postalCode: z.string().optional(),
    isDefault: z.boolean().optional(),
  }),
  agreeToTerms: z.boolean().refine((val) => val === true, {
    message: "Bạn phải đồng ý với điều khoản bán hàng",
  }),
});

const steps = [
  {
    name: "personal",
    schema: personalInfoSchema,
    fields: ["firstName", "lastName", "email", "phoneNumber"],
  },
  {
    name: "business",
    schema: businessInfoSchema,
    fields: ["businessName", "businessType", "taxId"],
  },
  {
    name: "store",
    schema: storeInfoSchema,
    fields: ["storeName", "storeDescription", "storeCategory"],
  },
  {
    name: "address",
    schema: addressInfoSchema,
    fields: ["address", "agreeToTerms"],
  },
];

export function MultiStepSellerRegistration() {
  const form = useMultiStepForm({
    steps,
    finalSchema: sellerRegistrationSchema,
    onSubmit: async (data: SellerRegistrationFormData) => {
      console.log("Seller registration submitted:", data);
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert("Đăng ký bán hàng thành công!");
    },
  });

  const stepTitles = [
    "Thông tin cá nhân",
    "Thông tin doanh nghiệp", 
    "Thông tin cửa hàng",
    "Địa chỉ & Xác nhận",
  ];

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Đăng ký bán hàng
        </h2>
        <p className="text-gray-600">
          Hoàn thành các bước sau để trở thành người bán trên Zenera
        </p>
      </div>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {stepTitles.map((title, index) => (
            <div key={index} className="flex items-center">
              <div
                className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium",
                  index < form.currentStep
                    ? "bg-green-500 text-white"
                    : index === form.currentStep
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-600"
                )}
              >
                {index < form.currentStep ? "✓" : index + 1}
              </div>
              {index < stepTitles.length - 1 && (
                <div
                  className={cn(
                    "w-16 h-1 mx-2",
                    index < form.currentStep ? "bg-green-500" : "bg-gray-200"
                  )}
                />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-between mt-2">
          {stepTitles.map((title, index) => (
            <div
              key={index}
              className={cn(
                "text-xs text-center",
                index === form.currentStep
                  ? "text-blue-600 font-medium"
                  : "text-gray-500"
              )}
              style={{ width: "120px" }}
            >
              {title}
            </div>
          ))}
        </div>
      </div>

      {/* Form Content */}
      <form onSubmit={form.handleSubmit(form.submitForm)} className="space-y-6">
        {/* Step 1: Personal Info */}
        {form.currentStep === 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Thông tin cá nhân</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <ControlledInput
                name="firstName"
                control={form.control}
                label="Tên"
                placeholder="Nhập tên của bạn"
                required
              />

              <ControlledInput
                name="lastName"
                control={form.control}
                label="Họ"
                placeholder="Nhập họ của bạn"
                required
              />
            </div>

            <ControlledInput
              name="email"
              control={form.control}
              label="Email"
              type="email"
              placeholder="Nhập email của bạn"
              required
            />

            <ControlledInput
              name="phoneNumber"
              control={form.control}
              label="Số điện thoại"
              type="tel"
              placeholder="Nhập số điện thoại"
              required
            />
          </div>
        )}

        {/* Step 2: Business Info */}
        {form.currentStep === 1 && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Thông tin doanh nghiệp</h3>
            
            <ControlledInput
              name="businessName"
              control={form.control}
              label="Tên doanh nghiệp"
              placeholder="Nhập tên doanh nghiệp"
              required
            />

            <ControlledSelect
              name="businessType"
              control={form.control}
              label="Loại hình kinh doanh"
              placeholder="Chọn loại hình kinh doanh"
              options={[
                { value: "individual", label: "Cá nhân" },
                { value: "company", label: "Công ty" },
              ]}
              required
            />

            <ControlledInput
              name="taxId"
              control={form.control}
              label="Mã số thuế"
              placeholder="Nhập mã số thuế (tùy chọn)"
            />
          </div>
        )}

        {/* Step 3: Store Info */}
        {form.currentStep === 2 && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Thông tin cửa hàng</h3>
            
            <ControlledInput
              name="storeName"
              control={form.control}
              label="Tên cửa hàng"
              placeholder="Nhập tên cửa hàng"
              required
            />

            <ControlledTextarea
              name="storeDescription"
              control={form.control}
              label="Mô tả cửa hàng"
              placeholder="Mô tả về cửa hàng của bạn"
              rows={4}
            />

            <ControlledSelect
              name="storeCategory"
              control={form.control}
              label="Danh mục cửa hàng"
              placeholder="Chọn danh mục chính"
              options={[
                { value: "electronics", label: "Điện tử" },
                { value: "fashion", label: "Thời trang" },
                { value: "home", label: "Gia dụng" },
                { value: "books", label: "Sách" },
                { value: "sports", label: "Thể thao" },
              ]}
              required
            />
          </div>
        )}

        {/* Step 4: Address & Confirmation */}
        {form.currentStep === 3 && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Địa chỉ & Xác nhận</h3>
            
            <ControlledInput
              name="address.name"
              control={form.control}
              label="Tên địa chỉ"
              placeholder="Ví dụ: Văn phòng chính"
              required
            />

            <ControlledInput
              name="address.phone"
              control={form.control}
              label="Số điện thoại liên hệ"
              type="tel"
              placeholder="Số điện thoại tại địa chỉ này"
              required
            />

            <ControlledInput
              name="address.address"
              control={form.control}
              label="Địa chỉ chi tiết"
              placeholder="Số nhà, tên đường"
              required
            />

            <div className="grid grid-cols-3 gap-4">
              <ControlledInput
                name="address.ward"
                control={form.control}
                label="Phường/Xã"
                placeholder="Phường/Xã"
                required
              />

              <ControlledInput
                name="address.district"
                control={form.control}
                label="Quận/Huyện"
                placeholder="Quận/Huyện"
                required
              />

              <ControlledInput
                name="address.province"
                control={form.control}
                label="Tỉnh/TP"
                placeholder="Tỉnh/Thành phố"
                required
              />
            </div>

            <ControlledSelect
              name="address.type"
              control={form.control}
              label="Loại địa chỉ"
              options={[
                { value: "work", label: "Văn phòng" },
                { value: "home", label: "Nhà riêng" },
                { value: "other", label: "Khác" },
              ]}
              required
            />

            <ControlledCheckbox
              name="agreeToTerms"
              control={form.control}
              label="Tôi đồng ý với điều khoản bán hàng và chính sách của Zenera"
              required
            />
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6">
          <button
            type="button"
            onClick={form.goToPrevious}
            disabled={form.isFirstStep}
            className={cn(
              "px-4 py-2 text-sm font-medium rounded-md",
              form.isFirstStep
                ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            )}
          >
            Quay lại
          </button>

          {form.isLastStep ? (
            <FormSubmitButton
              isSubmitting={form.isSubmitting}
              submittingText="Đang đăng ký..."
            >
              Hoàn thành đăng ký
            </FormSubmitButton>
          ) : (
            <button
              type="button"
              onClick={form.goToNext}
              className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
            >
              Tiếp tục
            </button>
          )}
        </div>
      </form>

      {/* Debug Info */}
      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-medium mb-2">Debug Info:</h3>
        <pre className="text-xs text-gray-600">
          {JSON.stringify({
            currentStep: form.currentStep,
            completedSteps: form.completedSteps,
            isValid: form.formState.isValid,
            errors: form.formState.errors,
          }, null, 2)}
        </pre>
      </div>
    </div>
  );
}
