/**
 * Form Error Handling System
 * Inspired by Medoo's error processing and validation utilities
 */

import { z } from "zod";

/**
 * Form Error Types
 */
export interface FormError {
  field?: string;
  message: string;
  code?: string;
  type: 'validation' | 'server' | 'network' | 'unknown';
}

export interface FormErrorResponse {
  success: false;
  errors: FormError[];
  message?: string;
  code?: string;
}

export interface FormSuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

export type FormResponse<T = any> = FormErrorResponse | FormSuccessResponse<T>;

/**
 * Error Processing Utilities (inspired by Medoo's showErrorMessageByName)
 */
export const FormErrorHandler = {
  /**
   * Process Zod validation errors
   */
  processZodErrors: (error: z.ZodError): FormError[] => {
    return error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code,
      type: 'validation' as const,
    }));
  },

  /**
   * Process server errors
   */
  processServerError: (error: any): FormError[] => {
    if (error?.response?.data?.errors) {
      // Handle structured error response
      return error.response.data.errors.map((err: any) => ({
        field: err.field,
        message: err.message,
        code: err.code,
        type: 'server' as const,
      }));
    }

    if (error?.response?.data?.message) {
      return [{
        message: error.response.data.message,
        code: error.response?.status?.toString(),
        type: 'server' as const,
      }];
    }

    if (error?.message) {
      return [{
        message: error.message,
        type: 'network' as const,
      }];
    }

    return [{
      message: 'Có lỗi không xác định xảy ra',
      type: 'unknown' as const,
    }];
  },

  /**
   * Get error message by error name/code (inspired by Medoo)
   */
  getErrorMessageByName: (errorName: string): string => {
    const errorMessages: Record<string, string> = {
      // Validation errors
      'required': 'Trường này là bắt buộc',
      'invalid_email': 'Email không hợp lệ',
      'invalid_phone': 'Số điện thoại không hợp lệ',
      'password_too_short': 'Mật khẩu quá ngắn',
      'password_mismatch': 'Mật khẩu xác nhận không khớp',
      
      // Server errors
      'user_not_found': 'Không tìm thấy người dùng',
      'invalid_credentials': 'Thông tin đăng nhập không chính xác',
      'email_already_exists': 'Email đã được sử dụng',
      'username_already_exists': 'Tên đăng nhập đã được sử dụng',
      'unauthorized': 'Bạn không có quyền thực hiện hành động này',
      'forbidden': 'Truy cập bị từ chối',
      'not_found': 'Không tìm thấy tài nguyên',
      'internal_server_error': 'Lỗi máy chủ nội bộ',
      
      // Network errors
      'network_error': 'Lỗi kết nối mạng',
      'timeout': 'Hết thời gian chờ',
      'connection_refused': 'Không thể kết nối đến máy chủ',
      
      // Business logic errors
      'insufficient_balance': 'Số dư không đủ',
      'product_out_of_stock': 'Sản phẩm đã hết hàng',
      'order_already_processed': 'Đơn hàng đã được xử lý',
      'invalid_coupon': 'Mã giảm giá không hợp lệ',
      'coupon_expired': 'Mã giảm giá đã hết hạn',
      'coupon_used': 'Mã giảm giá đã được sử dụng',
    };

    return errorMessages[errorName] || 'Có lỗi xảy ra';
  },

  /**
   * Show form validation fail message (inspired by Medoo)
   */
  showFormValidateFailMessage: (
    message: string = 'Dữ liệu form không chính xác',
    description: string = 'Vui lòng kiểm tra lại thông tin và thử lại'
  ) => {
    // In a real app, this would show a toast/notification
    console.error('Form Validation Error:', { message, description });
    
    // You can integrate with your notification system here
    // For example: toast.error(message, { description });
  },

  /**
   * Format errors for display
   */
  formatErrorsForDisplay: (errors: FormError[]): Record<string, string> => {
    const fieldErrors: Record<string, string> = {};
    
    errors.forEach(error => {
      if (error.field) {
        fieldErrors[error.field] = error.message;
      }
    });
    
    return fieldErrors;
  },

  /**
   * Get general error message from errors array
   */
  getGeneralErrorMessage: (errors: FormError[]): string | null => {
    const generalError = errors.find(error => !error.field);
    return generalError?.message || null;
  },

  /**
   * Check if errors contain specific field
   */
  hasFieldError: (errors: FormError[], fieldName: string): boolean => {
    return errors.some(error => error.field === fieldName);
  },

  /**
   * Get field error message
   */
  getFieldErrorMessage: (errors: FormError[], fieldName: string): string | null => {
    const fieldError = errors.find(error => error.field === fieldName);
    return fieldError?.message || null;
  },

  /**
   * Validate form data and return errors
   */
  validateFormData: <T>(
    data: T,
    schema: z.ZodSchema<T>
  ): { isValid: boolean; errors: FormError[] } => {
    try {
      schema.parse(data);
      return { isValid: true, errors: [] };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          isValid: false,
          errors: FormErrorHandler.processZodErrors(error),
        };
      }
      return {
        isValid: false,
        errors: [{
          message: 'Validation error',
          type: 'validation',
        }],
      };
    }
  },

  /**
   * Handle form submission errors
   */
  handleSubmissionError: (error: any): FormError[] => {
    if (error instanceof z.ZodError) {
      return FormErrorHandler.processZodErrors(error);
    }
    
    return FormErrorHandler.processServerError(error);
  },

  /**
   * Create error response
   */
  createErrorResponse: (errors: FormError[], message?: string): FormErrorResponse => {
    return {
      success: false,
      errors,
      message: message || FormErrorHandler.getGeneralErrorMessage(errors) || 'Có lỗi xảy ra',
    };
  },

  /**
   * Create success response
   */
  createSuccessResponse: <T>(data: T, message?: string): FormSuccessResponse<T> => {
    return {
      success: true,
      data,
      message,
    };
  },

  /**
   * Check if response is error
   */
  isErrorResponse: (response: FormResponse): response is FormErrorResponse => {
    return !response.success;
  },

  /**
   * Check if response is success
   */
  isSuccessResponse: <T>(response: FormResponse<T>): response is FormSuccessResponse<T> => {
    return response.success;
  },
};

/**
 * Form Error Context for global error handling
 */
export interface FormErrorContextType {
  errors: FormError[];
  setErrors: (errors: FormError[]) => void;
  clearErrors: () => void;
  addError: (error: FormError) => void;
  removeError: (field?: string) => void;
  hasErrors: boolean;
  getFieldError: (field: string) => string | null;
}

import { createContext, useContext, useState } from "react";

const FormErrorContext = createContext<FormErrorContextType | null>(null);

export function FormErrorProvider({ children }: { children: React.ReactNode }) {
  const [errors, setErrors] = useState<FormError[]>([]);

  const clearErrors = () => setErrors([]);
  
  const addError = (error: FormError) => {
    setErrors(prev => [...prev.filter(e => e.field !== error.field), error]);
  };
  
  const removeError = (field?: string) => {
    if (field) {
      setErrors(prev => prev.filter(e => e.field !== field));
    } else {
      setErrors(prev => prev.filter(e => e.field));
    }
  };

  const getFieldError = (field: string) => {
    return FormErrorHandler.getFieldErrorMessage(errors, field);
  };

  return (
    <FormErrorContext.Provider value={{
      errors,
      setErrors,
      clearErrors,
      addError,
      removeError,
      hasErrors: errors.length > 0,
      getFieldError,
    }}>
      {children}
    </FormErrorContext.Provider>
  );
}

export function useFormErrorContext() {
  const context = useContext(FormErrorContext);
  if (!context) {
    throw new Error('useFormErrorContext must be used within FormErrorProvider');
  }
  return context;
}
