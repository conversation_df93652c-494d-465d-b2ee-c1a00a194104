/**
 * Products API Service
 */

import { apiClient, API_ENDPOINTS } from '../api-client';

// Types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: string;
  tags: string[];
  inStock: boolean;
  stockQuantity: number;
  rating: number;
  reviewCount: number;
  sellerId: string;
  seller?: {
    id: string;
    name: string;
    rating: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateProductRequest {
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: string;
  tags: string[];
  stockQuantity: number;
}

export interface UpdateProductRequest extends Partial<CreateProductRequest> {}

export interface ProductsQuery {
  page?: number;
  limit?: number;
  category?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  sortBy?: 'name' | 'price' | 'rating' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

export interface ProductsResponse {
  products: Product[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Simple array response for current API
export type ProductsArrayResponse = Product[];

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  productCount: number;
}

// Products API Service
export const productsApi = {
  /**
   * Get all products với pagination và filtering
   */
  async getProducts(query: ProductsQuery = {}): Promise<ProductsResponse> {
    const searchParams = new URLSearchParams();

    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString());
      }
    });

    const endpoint = `${API_ENDPOINTS.PRODUCTS.BASE}?${searchParams.toString()}`;
    const products = await apiClient.get<ProductsArrayResponse>(endpoint);

    // Transform array response to expected format
    return {
      products: Array.isArray(products) ? products : [],
      pagination: {
        page: query.page || 1,
        limit: query.limit || 10,
        total: Array.isArray(products) ? products.length : 0,
        totalPages: 1,
      },
    };
  },

  /**
   * Get single product by ID
   */
  async getProduct(id: string): Promise<Product> {
    return apiClient.get<Product>(API_ENDPOINTS.PRODUCTS.BY_ID(id));
  },

  /**
   * Create new product (seller/admin only)
   */
  async createProduct(productData: CreateProductRequest): Promise<Product> {
    return apiClient.post<Product>(API_ENDPOINTS.PRODUCTS.BASE, productData);
  },

  /**
   * Update product (seller/admin only)
   */
  async updateProduct(
    id: string,
    productData: UpdateProductRequest
  ): Promise<Product> {
    return apiClient.patch<Product>(
      API_ENDPOINTS.PRODUCTS.BY_ID(id),
      productData
    );
  },

  /**
   * Delete product (seller/admin only)
   */
  async deleteProduct(id: string): Promise<void> {
    return apiClient.delete(API_ENDPOINTS.PRODUCTS.BY_ID(id));
  },

  /**
   * Search products
   */
  async searchProducts(
    searchTerm: string,
    filters: Omit<ProductsQuery, 'search'> = {}
  ): Promise<ProductsResponse> {
    return this.getProducts({ ...filters, search: searchTerm });
  },

  /**
   * Get product categories
   */
  async getCategories(): Promise<Category[]> {
    return apiClient.get<Category[]>(API_ENDPOINTS.PRODUCTS.CATEGORIES);
  },

  /**
   * Get featured products
   */
  async getFeaturedProducts(limit: number = 8): Promise<Product[]> {
    try {
      const response = await this.getProducts({
        limit,
        sortBy: 'rating',
        sortOrder: 'desc',
      });
      return response.products || [];
    } catch (error) {
      console.error('Error fetching featured products:', error);
      return [];
    }
  },

  /**
   * Get products by category
   */
  async getProductsByCategory(
    category: string,
    query: Omit<ProductsQuery, 'category'> = {}
  ): Promise<ProductsResponse> {
    return this.getProducts({ ...query, category });
  },

  /**
   * Get related products
   */
  async getRelatedProducts(
    productId: string,
    limit: number = 4
  ): Promise<Product[]> {
    // First get the product to know its category
    const product = await this.getProduct(productId);
    
    // Then get products from same category, excluding current product
    const response = await this.getProducts({
      category: product.category,
      limit: limit + 1, // Get one extra to exclude current
    });
    
    // Filter out current product
    return response.products.filter(p => p.id !== productId).slice(0, limit);
  },
};
