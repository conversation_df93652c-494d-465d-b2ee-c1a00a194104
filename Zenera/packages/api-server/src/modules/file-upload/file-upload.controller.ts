import {
  Controller,
  Post,
  Get,
  Delete,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  Param,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  Req,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { FileUploadService, UploadedFile as UploadedFileType } from './file-upload.service';
import { Request } from 'express';

@ApiTags('File Upload')
@Controller('upload')
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Post('single')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Upload single file' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: 'File uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadSingleFile(
    @UploadedFile() file: Express.Multer.File,
    @Query('folder') folder: string = 'general',
    @Req() req: Request,
  ): Promise<UploadedFileType> {
    if (!file) {
      throw new HttpException('No file uploaded', HttpStatus.BAD_REQUEST);
    }

    try {
      return await this.fileUploadService.uploadSingleFile(file, folder);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to upload file',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('multiple')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Upload multiple files' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: 'Files uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @UseInterceptors(FilesInterceptor('files', 10)) // Maximum 10 files
  async uploadMultipleFiles(
    @UploadedFiles() files: Express.Multer.File[],
    @Query('folder') folder: string = 'general',
    @Req() req: Request,
  ): Promise<UploadedFileType[]> {
    if (!files || files.length === 0) {
      throw new HttpException('No files uploaded', HttpStatus.BAD_REQUEST);
    }

    try {
      return await this.fileUploadService.uploadMultipleFiles(files, folder);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to upload files',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('images')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Upload product images' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: 'Images uploaded successfully' })
  @UseInterceptors(FilesInterceptor('images', 10))
  async uploadImages(
    @UploadedFiles() files: Express.Multer.File[],
    @Req() req: Request,
  ): Promise<UploadedFileType[]> {
    if (!files || files.length === 0) {
      throw new HttpException('No images uploaded', HttpStatus.BAD_REQUEST);
    }

    // Validate that all files are images
    const invalidFiles = files.filter(file => !file.mimetype.startsWith('image/'));
    if (invalidFiles.length > 0) {
      throw new HttpException('Only image files are allowed', HttpStatus.BAD_REQUEST);
    }

    try {
      return await this.fileUploadService.uploadMultipleFiles(files, 'images');
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to upload images',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('documents')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Upload documents' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: 'Documents uploaded successfully' })
  @UseInterceptors(FilesInterceptor('documents', 5))
  async uploadDocuments(
    @UploadedFiles() files: Express.Multer.File[],
    @Req() req: Request,
  ): Promise<UploadedFileType[]> {
    if (!files || files.length === 0) {
      throw new HttpException('No documents uploaded', HttpStatus.BAD_REQUEST);
    }

    try {
      return await this.fileUploadService.uploadMultipleFiles(files, 'documents');
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to upload documents',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('avatar')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Upload user avatar' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: 'Avatar uploaded successfully' })
  @UseInterceptors(FileInterceptor('avatar'))
  async uploadAvatar(
    @UploadedFile() file: Express.Multer.File,
    @Req() req: Request,
  ): Promise<UploadedFileType> {
    if (!file) {
      throw new HttpException('No avatar uploaded', HttpStatus.BAD_REQUEST);
    }

    // Validate that file is an image
    if (!file.mimetype.startsWith('image/')) {
      throw new HttpException('Only image files are allowed for avatars', HttpStatus.BAD_REQUEST);
    }

    try {
      return await this.fileUploadService.uploadSingleFile(file, 'avatars');
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to upload avatar',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('info/:folder/:filename')
  @ApiOperation({ summary: 'Get file information' })
  @ApiResponse({ status: 200, description: 'File information retrieved successfully' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async getFileInfo(
    @Param('folder') folder: string,
    @Param('filename') filename: string,
  ): Promise<any> {
    try {
      return await this.fileUploadService.getFileInfo(filename, folder);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get file info',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':folder/:filename')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete file' })
  @ApiResponse({ status: 200, description: 'File deleted successfully' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async deleteFile(
    @Param('folder') folder: string,
    @Param('filename') filename: string,
  ): Promise<{ message: string }> {
    try {
      await this.fileUploadService.deleteFile(filename, folder);
      return { message: 'File deleted successfully' };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to delete file',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get upload statistics' })
  @ApiResponse({ status: 200, description: 'Upload statistics retrieved successfully' })
  async getUploadStats(): Promise<any> {
    try {
      return await this.fileUploadService.getUploadStats();
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get upload statistics',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('cleanup')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Clean up old files' })
  @ApiResponse({ status: 200, description: 'Cleanup completed successfully' })
  async cleanupOldFiles(
    @Query('days') days: string = '30',
  ): Promise<{ deletedFiles: number; freedSpace: number }> {
    try {
      const daysOld = parseInt(days, 10);
      if (isNaN(daysOld) || daysOld < 1) {
        throw new HttpException('Invalid days parameter', HttpStatus.BAD_REQUEST);
      }
      
      return await this.fileUploadService.cleanupOldFiles(daysOld);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to cleanup files',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
