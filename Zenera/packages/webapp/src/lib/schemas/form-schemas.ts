/**
 * E-commerce Form Schemas
 * Schema definitions for various e-commerce forms using Medoo-inspired patterns
 */

import { z } from "zod";
import { 
  FormSchemaItem, 
  FormSection,
  createInputSchemaItem,
  createSelectSchemaItem,
  createTextareaSchemaItem,
  createCheckboxSchemaItem,
  createFileSchemaItem,
  createPhoneOrEmailSchemaItem,
} from "@/lib/forms/schema-form";

/**
 * Login Form Schema
 */
export const useLoginFormSchema = () => {
  return (): FormSchemaItem[] => [
    createPhoneOrEmailSchemaItem({
      name: "email",
      usingPhoneNumber: false,
      label: "Email",
      required: true,
    }),
    createInputSchemaItem({
      name: "password",
      type: "password",
      label: "Mật khẩu",
      placeholder: "Nhập mật khẩu của bạn",
      required: true,
    }),
    createCheckboxSchemaItem({
      name: "rememberM<PERSON>",
      label: "<PERSON><PERSON> nhớ đăng nhập",
    }),
  ];
};

/**
 * Register Form Schema
 */
export const useRegisterFormSchema = () => {
  return (): FormSchemaItem[] => [
    createInputSchemaItem({
      name: "firstName",
      label: "Tên",
      placeholder: "Nhập tên của bạn",
      required: true,
      colSpan: 1,
    }),
    createInputSchemaItem({
      name: "lastName",
      label: "Họ",
      placeholder: "Nhập họ của bạn",
      required: true,
      colSpan: 1,
    }),
    createPhoneOrEmailSchemaItem({
      name: "email",
      usingPhoneNumber: false,
      label: "Email",
      required: true,
    }),
    createInputSchemaItem({
      name: "username",
      label: "Tên đăng nhập",
      placeholder: "Nhập tên đăng nhập (tùy chọn)",
      validation: z.string()
        .min(3, "Tên đăng nhập phải có ít nhất 3 ký tự")
        .max(20, "Tên đăng nhập không được quá 20 ký tự")
        .regex(/^[a-zA-Z0-9_]+$/, "Tên đăng nhập chỉ được chứa chữ, số và dấu gạch dưới")
        .optional(),
    }),
    createPhoneOrEmailSchemaItem({
      name: "phoneNumber",
      usingPhoneNumber: true,
      label: "Số điện thoại",
    }),
    createInputSchemaItem({
      name: "password",
      type: "password",
      label: "Mật khẩu",
      placeholder: "Nhập mật khẩu",
      required: true,
      validation: z.string()
        .min(8, "Mật khẩu phải có ít nhất 8 ký tự")
        .regex(/[A-Z]/, "Mật khẩu phải có ít nhất 1 chữ hoa")
        .regex(/[a-z]/, "Mật khẩu phải có ít nhất 1 chữ thường")
        .regex(/[0-9]/, "Mật khẩu phải có ít nhất 1 số"),
      colSpan: 1,
    }),
    createInputSchemaItem({
      name: "confirmPassword",
      type: "password",
      label: "Xác nhận mật khẩu",
      placeholder: "Nhập lại mật khẩu",
      required: true,
      colSpan: 1,
    }),
    createSelectSchemaItem({
      name: "role",
      label: "Loại tài khoản",
      required: true,
      options: [
        { value: "customer", label: "Khách hàng" },
        { value: "seller", label: "Người bán" },
      ],
    }),
    createCheckboxSchemaItem({
      name: "agreeToTerms",
      label: "Tôi đồng ý với điều khoản sử dụng",
      required: true,
      validation: z.boolean().refine((val) => val === true, {
        message: "Bạn phải đồng ý với điều khoản sử dụng",
      }),
    }),
  ];
};

/**
 * Product Form Schema
 */
export const useProductFormSchema = () => {
  return (): FormSection[] => [
    {
      title: "Thông tin cơ bản",
      fields: [
        createInputSchemaItem({
          name: "name",
          label: "Tên sản phẩm",
          placeholder: "Nhập tên sản phẩm",
          required: true,
        }),
        createTextareaSchemaItem({
          name: "description",
          label: "Mô tả sản phẩm",
          placeholder: "Nhập mô tả chi tiết sản phẩm",
          componentProps: { rows: 4 },
        }),
        createTextareaSchemaItem({
          name: "shortDescription",
          label: "Mô tả ngắn",
          placeholder: "Nhập mô tả ngắn gọn",
          componentProps: { rows: 2 },
          validation: z.string().max(500, "Mô tả ngắn không được quá 500 ký tự").optional(),
        }),
      ],
    },
    {
      title: "Giá cả & Tồn kho",
      fields: [
        createInputSchemaItem({
          name: "price",
          type: "number",
          label: "Giá bán",
          placeholder: "0",
          required: true,
          min: 0,
          step: 0.01,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "compareAtPrice",
          type: "number",
          label: "Giá so sánh",
          placeholder: "0",
          min: 0,
          step: 0.01,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "cost",
          type: "number",
          label: "Giá vốn",
          placeholder: "0",
          min: 0,
          step: 0.01,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "inventory",
          type: "number",
          label: "Số lượng tồn kho",
          placeholder: "0",
          required: true,
          min: 0,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "sku",
          label: "Mã SKU",
          placeholder: "Nhập mã SKU (tùy chọn)",
        }),
      ],
    },
    {
      title: "Phân loại",
      fields: [
        createSelectSchemaItem({
          name: "categoryId",
          label: "Danh mục",
          placeholder: "Chọn danh mục sản phẩm",
          required: true,
          options: [
            { value: "electronics", label: "Điện tử" },
            { value: "clothing", label: "Thời trang" },
            { value: "books", label: "Sách" },
            { value: "home", label: "Gia dụng" },
            { value: "sports", label: "Thể thao" },
          ],
        }),
        createSelectSchemaItem({
          name: "subcategoryId",
          label: "Danh mục con",
          placeholder: "Chọn danh mục con (tùy chọn)",
          options: [],
          when: {
            field: "categoryId",
            condition: (value) => !!value,
          },
        }),
        createInputSchemaItem({
          name: "brand",
          label: "Thương hiệu",
          placeholder: "Nhập tên thương hiệu",
        }),
        createInputSchemaItem({
          name: "tags",
          label: "Tags",
          placeholder: "Nhập tags, cách nhau bởi dấu phẩy",
          hint: "Ví dụ: điện thoại, smartphone, apple",
        }),
      ],
    },
    {
      title: "Hình ảnh",
      fields: [
        createFileSchemaItem({
          name: "mainImage",
          label: "Hình ảnh chính",
          accept: "image/*",
          hint: "Chọn hình ảnh chính cho sản phẩm",
        }),
        createFileSchemaItem({
          name: "images",
          label: "Hình ảnh khác",
          accept: "image/*",
          multiple: true,
          hint: "Chọn các hình ảnh bổ sung (tối đa 10 ảnh)",
        }),
      ],
    },
    {
      title: "Thuộc tính vật lý",
      collapsible: true,
      defaultCollapsed: true,
      fields: [
        createInputSchemaItem({
          name: "weight",
          type: "number",
          label: "Trọng lượng (kg)",
          placeholder: "0",
          min: 0,
          step: 0.01,
        }),
        createInputSchemaItem({
          name: "dimensions.length",
          type: "number",
          label: "Chiều dài (cm)",
          placeholder: "0",
          min: 0,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "dimensions.width",
          type: "number",
          label: "Chiều rộng (cm)",
          placeholder: "0",
          min: 0,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "dimensions.height",
          type: "number",
          label: "Chiều cao (cm)",
          placeholder: "0",
          min: 0,
          colSpan: 1,
        }),
      ],
    },
    {
      title: "Cài đặt",
      fields: [
        createSelectSchemaItem({
          name: "status",
          label: "Trạng thái",
          required: true,
          options: [
            { value: "draft", label: "Bản nháp" },
            { value: "active", label: "Đang bán" },
            { value: "inactive", label: "Ngừng bán" },
          ],
        }),
        createCheckboxSchemaItem({
          name: "isDigital",
          label: "Sản phẩm số",
        }),
        createCheckboxSchemaItem({
          name: "isFeatured",
          label: "Sản phẩm nổi bật",
        }),
        createCheckboxSchemaItem({
          name: "requiresShipping",
          label: "Cần vận chuyển",
        }),
        createCheckboxSchemaItem({
          name: "trackInventory",
          label: "Theo dõi tồn kho",
        }),
        createCheckboxSchemaItem({
          name: "allowBackorder",
          label: "Cho phép đặt hàng khi hết hàng",
        }),
      ],
    },
  ];
};

/**
 * Address Form Schema
 */
export const useAddressFormSchema = () => {
  return (): FormSchemaItem[] => [
    createSelectSchemaItem({
      name: "type",
      label: "Loại địa chỉ",
      required: true,
      options: [
        { value: "home", label: "Nhà riêng" },
        { value: "work", label: "Văn phòng" },
        { value: "other", label: "Khác" },
      ],
    }),
    createInputSchemaItem({
      name: "name",
      label: "Tên địa chỉ",
      placeholder: "Ví dụ: Nhà riêng, Văn phòng",
      required: true,
    }),
    createPhoneOrEmailSchemaItem({
      name: "phone",
      usingPhoneNumber: true,
      label: "Số điện thoại",
      required: true,
    }),
    createInputSchemaItem({
      name: "address",
      label: "Địa chỉ chi tiết",
      placeholder: "Số nhà, tên đường",
      required: true,
    }),
    createInputSchemaItem({
      name: "ward",
      label: "Phường/Xã",
      placeholder: "Chọn phường/xã",
      required: true,
      colSpan: 1,
    }),
    createInputSchemaItem({
      name: "district",
      label: "Quận/Huyện",
      placeholder: "Chọn quận/huyện",
      required: true,
      colSpan: 1,
    }),
    createInputSchemaItem({
      name: "province",
      label: "Tỉnh/Thành phố",
      placeholder: "Chọn tỉnh/thành phố",
      required: true,
      colSpan: 1,
    }),
    createInputSchemaItem({
      name: "postalCode",
      label: "Mã bưu điện",
      placeholder: "Nhập mã bưu điện (tùy chọn)",
      colSpan: 1,
    }),
    createCheckboxSchemaItem({
      name: "isDefault",
      label: "Đặt làm địa chỉ mặc định",
    }),
  ];
};

/**
 * Contact Form Schema
 */
export const useContactFormSchema = () => {
  return (): FormSchemaItem[] => [
    createInputSchemaItem({
      name: "name",
      label: "Họ và tên",
      placeholder: "Nhập họ và tên của bạn",
      required: true,
    }),
    createPhoneOrEmailSchemaItem({
      name: "email",
      usingPhoneNumber: false,
      label: "Email",
      required: true,
    }),
    createPhoneOrEmailSchemaItem({
      name: "phone",
      usingPhoneNumber: true,
      label: "Số điện thoại",
    }),
    createSelectSchemaItem({
      name: "subject",
      label: "Chủ đề",
      placeholder: "Chọn chủ đề",
      required: true,
      options: [
        { value: "general", label: "Câu hỏi chung" },
        { value: "support", label: "Hỗ trợ kỹ thuật" },
        { value: "billing", label: "Thanh toán" },
        { value: "partnership", label: "Hợp tác" },
        { value: "other", label: "Khác" },
      ],
    }),
    createTextareaSchemaItem({
      name: "message",
      label: "Tin nhắn",
      placeholder: "Nhập tin nhắn của bạn",
      required: true,
      componentProps: { rows: 5 },
      validation: z.string().min(10, "Tin nhắn phải có ít nhất 10 ký tự"),
    }),
  ];
};
