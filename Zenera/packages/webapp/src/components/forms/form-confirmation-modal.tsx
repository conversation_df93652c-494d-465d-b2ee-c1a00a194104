"use client";

import { useState } from "react";
import { cn } from "@/lib/utils";

/**
 * Form Confirmation Modal
 * Inspired by Medoo's HFormConfirmModal for form submission confirmation
 */

export interface FormConfirmationModalProps {
  visible: boolean;
  title?: string;
  content?: string;
  confirmText?: string;
  cancelText?: string;
  loading?: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  type?: 'info' | 'warning' | 'error' | 'success';
}

export function FormConfirmationModal({
  visible,
  title = "Xác nhận",
  content = "Bạn có chắc chắn muốn thực hiện hành động này?",
  confirmText = "Xác nhận",
  cancelText = "Hủy",
  loading = false,
  onConfirm,
  onCancel,
  type = 'info',
}: FormConfirmationModalProps) {
  if (!visible) return null;

  const typeStyles = {
    info: "text-blue-600",
    warning: "text-yellow-600", 
    error: "text-red-600",
    success: "text-green-600",
  };

  const typeIcons = {
    info: "ℹ️",
    warning: "⚠️",
    error: "❌",
    success: "✅",
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onCancel}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
        {/* Header */}
        <div className="flex items-center space-x-3 mb-4">
          <span className="text-2xl">{typeIcons[type]}</span>
          <h3 className={cn("text-lg font-semibold", typeStyles[type])}>
            {title}
          </h3>
        </div>

        {/* Content */}
        <div className="mb-6">
          <p className="text-gray-600">{content}</p>
        </div>

        {/* Actions */}
        <div className="flex space-x-3 justify-end">
          <button
            type="button"
            onClick={onCancel}
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
          >
            {cancelText}
          </button>
          <button
            type="button"
            onClick={onConfirm}
            disabled={loading}
            className={cn(
              "px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50",
              type === 'error' 
                ? "bg-red-600 hover:bg-red-700 focus:ring-red-500"
                : type === 'warning'
                ? "bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500"
                : type === 'success'
                ? "bg-green-600 hover:bg-green-700 focus:ring-green-500"
                : "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500"
            )}
          >
            {loading && (
              <svg
                className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
            )}
            {loading ? "Đang xử lý..." : confirmText}
          </button>
        </div>
      </div>
    </div>
  );
}

/**
 * Hook for managing confirmation modal state
 */
export function useFormConfirmation() {
  const [visible, setVisible] = useState(false);
  const [config, setConfig] = useState<Partial<FormConfirmationModalProps>>({});

  const showConfirmation = (options: Partial<FormConfirmationModalProps> = {}) => {
    setConfig(options);
    setVisible(true);
  };

  const hideConfirmation = () => {
    setVisible(false);
    setConfig({});
  };

  const confirmAndExecute = async (
    action: () => Promise<void>,
    options: Partial<FormConfirmationModalProps> = {}
  ) => {
    return new Promise<void>((resolve, reject) => {
      showConfirmation({
        ...options,
        onConfirm: async () => {
          try {
            await action();
            hideConfirmation();
            resolve();
          } catch (error) {
            hideConfirmation();
            reject(error);
          }
        },
        onCancel: () => {
          hideConfirmation();
          reject(new Error('User cancelled'));
        },
      });
    });
  };

  return {
    visible,
    config,
    showConfirmation,
    hideConfirmation,
    confirmAndExecute,
  };
}

/**
 * Form Confirmation Provider
 */
export interface FormConfirmationContextType {
  confirm: (options: Partial<FormConfirmationModalProps>) => Promise<void>;
}

import { createContext, useContext } from "react";

const FormConfirmationContext = createContext<FormConfirmationContextType | null>(null);

export function FormConfirmationProvider({ children }: { children: React.ReactNode }) {
  const { visible, config, confirmAndExecute, hideConfirmation } = useFormConfirmation();

  const confirm = (options: Partial<FormConfirmationModalProps>) => {
    return new Promise<void>((resolve, reject) => {
      confirmAndExecute(
        async () => resolve(),
        {
          ...options,
          onCancel: () => reject(new Error('User cancelled')),
        }
      );
    });
  };

  return (
    <FormConfirmationContext.Provider value={{ confirm }}>
      {children}
      <FormConfirmationModal
        visible={visible}
        {...config}
        onCancel={hideConfirmation}
      />
    </FormConfirmationContext.Provider>
  );
}

export function useFormConfirmationContext() {
  const context = useContext(FormConfirmationContext);
  if (!context) {
    throw new Error('useFormConfirmationContext must be used within FormConfirmationProvider');
  }
  return context;
}
