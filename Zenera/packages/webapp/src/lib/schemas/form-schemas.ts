/**
 * E-commerce Form Schemas
 * Schema definitions for various e-commerce forms using Medoo-inspired patterns
 */

import { z } from "zod";
import { 
  FormSchemaItem, 
  FormSection,
  createInputSchemaItem,
  createSelectSchemaItem,
  createTextareaSchemaItem,
  createCheckboxSchemaItem,
  createFileSchemaItem,
  createPhoneOrEmailSchemaItem,
} from "@/lib/forms/schema-form";

/**
 * Login Form Schema
 */
export const useLoginFormSchema = () => {
  return (): FormSchemaItem[] => [
    createPhoneOrEmailSchemaItem({
      name: "email",
      usingPhoneNumber: false,
      label: "Email",
      required: true,
    }),
    createInputSchemaItem({
      name: "password",
      type: "password",
      label: "Mật khẩu",
      placeholder: "Nhập mật khẩu của bạn",
      required: true,
    }),
    createCheckboxSchemaItem({
      name: "rememberM<PERSON>",
      label: "<PERSON><PERSON> nhớ đăng nhập",
    }),
  ];
};

/**
 * Register Form Schema
 */
export const useRegisterFormSchema = () => {
  return (): FormSchemaItem[] => [
    createInputSchemaItem({
      name: "firstName",
      label: "Tên",
      placeholder: "Nhập tên của bạn",
      required: true,
      colSpan: 1,
    }),
    createInputSchemaItem({
      name: "lastName",
      label: "Họ",
      placeholder: "Nhập họ của bạn",
      required: true,
      colSpan: 1,
    }),
    createPhoneOrEmailSchemaItem({
      name: "email",
      usingPhoneNumber: false,
      label: "Email",
      required: true,
    }),
    createInputSchemaItem({
      name: "username",
      label: "Tên đăng nhập",
      placeholder: "Nhập tên đăng nhập (tùy chọn)",
      validation: z.string()
        .min(3, "Tên đăng nhập phải có ít nhất 3 ký tự")
        .max(20, "Tên đăng nhập không được quá 20 ký tự")
        .regex(/^[a-zA-Z0-9_]+$/, "Tên đăng nhập chỉ được chứa chữ, số và dấu gạch dưới")
        .optional(),
    }),
    createPhoneOrEmailSchemaItem({
      name: "phoneNumber",
      usingPhoneNumber: true,
      label: "Số điện thoại",
    }),
    createInputSchemaItem({
      name: "password",
      type: "password",
      label: "Mật khẩu",
      placeholder: "Nhập mật khẩu",
      required: true,
      validation: z.string()
        .min(8, "Mật khẩu phải có ít nhất 8 ký tự")
        .regex(/[A-Z]/, "Mật khẩu phải có ít nhất 1 chữ hoa")
        .regex(/[a-z]/, "Mật khẩu phải có ít nhất 1 chữ thường")
        .regex(/[0-9]/, "Mật khẩu phải có ít nhất 1 số"),
      colSpan: 1,
    }),
    createInputSchemaItem({
      name: "confirmPassword",
      type: "password",
      label: "Xác nhận mật khẩu",
      placeholder: "Nhập lại mật khẩu",
      required: true,
      colSpan: 1,
    }),
    createSelectSchemaItem({
      name: "role",
      label: "Loại tài khoản",
      required: true,
      options: [
        { value: "customer", label: "Khách hàng" },
        { value: "seller", label: "Người bán" },
      ],
    }),
    createCheckboxSchemaItem({
      name: "agreeToTerms",
      label: "Tôi đồng ý với điều khoản sử dụng",
      required: true,
      validation: z.boolean().refine((val) => val === true, {
        message: "Bạn phải đồng ý với điều khoản sử dụng",
      }),
    }),
  ];
};

/**
 * Product Form Schema
 */
export const useProductFormSchema = () => {
  return (): FormSection[] => [
    {
      title: "Thông tin cơ bản",
      fields: [
        createInputSchemaItem({
          name: "name",
          label: "Tên sản phẩm",
          placeholder: "Nhập tên sản phẩm",
          required: true,
        }),
        createTextareaSchemaItem({
          name: "description",
          label: "Mô tả sản phẩm",
          placeholder: "Nhập mô tả chi tiết sản phẩm",
          componentProps: { rows: 4 },
        }),
        createTextareaSchemaItem({
          name: "shortDescription",
          label: "Mô tả ngắn",
          placeholder: "Nhập mô tả ngắn gọn",
          componentProps: { rows: 2 },
          validation: z.string().max(500, "Mô tả ngắn không được quá 500 ký tự").optional(),
        }),
      ],
    },
    {
      title: "Giá cả & Tồn kho",
      fields: [
        createInputSchemaItem({
          name: "price",
          type: "number",
          label: "Giá bán",
          placeholder: "0",
          required: true,
          min: 0,
          step: 0.01,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "compareAtPrice",
          type: "number",
          label: "Giá so sánh",
          placeholder: "0",
          min: 0,
          step: 0.01,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "cost",
          type: "number",
          label: "Giá vốn",
          placeholder: "0",
          min: 0,
          step: 0.01,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "inventory",
          type: "number",
          label: "Số lượng tồn kho",
          placeholder: "0",
          required: true,
          min: 0,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "sku",
          label: "Mã SKU",
          placeholder: "Nhập mã SKU (tùy chọn)",
        }),
      ],
    },
    {
      title: "Phân loại",
      fields: [
        createSelectSchemaItem({
          name: "categoryId",
          label: "Danh mục",
          placeholder: "Chọn danh mục sản phẩm",
          required: true,
          options: [
            { value: "electronics", label: "Điện tử" },
            { value: "clothing", label: "Thời trang" },
            { value: "books", label: "Sách" },
            { value: "home", label: "Gia dụng" },
            { value: "sports", label: "Thể thao" },
          ],
        }),
        createSelectSchemaItem({
          name: "subcategoryId",
          label: "Danh mục con",
          placeholder: "Chọn danh mục con (tùy chọn)",
          options: [],
          when: {
            field: "categoryId",
            condition: (value) => !!value,
          },
        }),
        createInputSchemaItem({
          name: "brand",
          label: "Thương hiệu",
          placeholder: "Nhập tên thương hiệu",
        }),
        createInputSchemaItem({
          name: "tags",
          label: "Tags",
          placeholder: "Nhập tags, cách nhau bởi dấu phẩy",
          hint: "Ví dụ: điện thoại, smartphone, apple",
        }),
      ],
    },
    {
      title: "Hình ảnh",
      fields: [
        createFileSchemaItem({
          name: "mainImage",
          label: "Hình ảnh chính",
          accept: "image/*",
          hint: "Chọn hình ảnh chính cho sản phẩm",
        }),
        createFileSchemaItem({
          name: "images",
          label: "Hình ảnh khác",
          accept: "image/*",
          multiple: true,
          hint: "Chọn các hình ảnh bổ sung (tối đa 10 ảnh)",
        }),
      ],
    },
    {
      title: "Thuộc tính vật lý",
      collapsible: true,
      defaultCollapsed: true,
      fields: [
        createInputSchemaItem({
          name: "weight",
          type: "number",
          label: "Trọng lượng (kg)",
          placeholder: "0",
          min: 0,
          step: 0.01,
        }),
        createInputSchemaItem({
          name: "dimensions.length",
          type: "number",
          label: "Chiều dài (cm)",
          placeholder: "0",
          min: 0,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "dimensions.width",
          type: "number",
          label: "Chiều rộng (cm)",
          placeholder: "0",
          min: 0,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "dimensions.height",
          type: "number",
          label: "Chiều cao (cm)",
          placeholder: "0",
          min: 0,
          colSpan: 1,
        }),
      ],
    },
    {
      title: "Cài đặt",
      fields: [
        createSelectSchemaItem({
          name: "status",
          label: "Trạng thái",
          required: true,
          options: [
            { value: "draft", label: "Bản nháp" },
            { value: "active", label: "Đang bán" },
            { value: "inactive", label: "Ngừng bán" },
          ],
        }),
        createCheckboxSchemaItem({
          name: "isDigital",
          label: "Sản phẩm số",
        }),
        createCheckboxSchemaItem({
          name: "isFeatured",
          label: "Sản phẩm nổi bật",
        }),
        createCheckboxSchemaItem({
          name: "requiresShipping",
          label: "Cần vận chuyển",
        }),
        createCheckboxSchemaItem({
          name: "trackInventory",
          label: "Theo dõi tồn kho",
        }),
        createCheckboxSchemaItem({
          name: "allowBackorder",
          label: "Cho phép đặt hàng khi hết hàng",
        }),
      ],
    },
  ];
};

/**
 * Address Form Schema
 */
export const useAddressFormSchema = () => {
  return (): FormSchemaItem[] => [
    createSelectSchemaItem({
      name: "type",
      label: "Loại địa chỉ",
      required: true,
      options: [
        { value: "home", label: "Nhà riêng" },
        { value: "work", label: "Văn phòng" },
        { value: "other", label: "Khác" },
      ],
    }),
    createInputSchemaItem({
      name: "name",
      label: "Tên địa chỉ",
      placeholder: "Ví dụ: Nhà riêng, Văn phòng",
      required: true,
    }),
    createPhoneOrEmailSchemaItem({
      name: "phone",
      usingPhoneNumber: true,
      label: "Số điện thoại",
      required: true,
    }),
    createInputSchemaItem({
      name: "address",
      label: "Địa chỉ chi tiết",
      placeholder: "Số nhà, tên đường",
      required: true,
    }),
    createInputSchemaItem({
      name: "ward",
      label: "Phường/Xã",
      placeholder: "Chọn phường/xã",
      required: true,
      colSpan: 1,
    }),
    createInputSchemaItem({
      name: "district",
      label: "Quận/Huyện",
      placeholder: "Chọn quận/huyện",
      required: true,
      colSpan: 1,
    }),
    createInputSchemaItem({
      name: "province",
      label: "Tỉnh/Thành phố",
      placeholder: "Chọn tỉnh/thành phố",
      required: true,
      colSpan: 1,
    }),
    createInputSchemaItem({
      name: "postalCode",
      label: "Mã bưu điện",
      placeholder: "Nhập mã bưu điện (tùy chọn)",
      colSpan: 1,
    }),
    createCheckboxSchemaItem({
      name: "isDefault",
      label: "Đặt làm địa chỉ mặc định",
    }),
  ];
};

/**
 * Order Form Schema (inspired by zen-buy.be order schema)
 */
export const useOrderFormSchema = () => {
  return (): FormSection[] => [
    {
      title: "Thông tin đơn hàng",
      fields: [
        createInputSchemaItem({
          name: "order_number",
          label: "Mã đơn hàng",
          placeholder: "Tự động tạo",
          disabled: true,
        }),
        createSelectSchemaItem({
          name: "status",
          label: "Trạng thái đơn hàng",
          required: true,
          defaultValue: "pending",
          options: [
            { value: "pending", label: "Chờ xử lý" },
            { value: "confirmed", label: "Đã xác nhận" },
            { value: "processing", label: "Đang xử lý" },
            { value: "shipped", label: "Đã gửi hàng" },
            { value: "delivered", label: "Đã giao hàng" },
            { value: "cancelled", label: "Đã hủy" },
            { value: "returned", label: "Đã trả hàng" },
          ],
        }),
        createSelectSchemaItem({
          name: "payment_status",
          label: "Trạng thái thanh toán",
          required: true,
          defaultValue: "pending",
          options: [
            { value: "pending", label: "Chờ thanh toán" },
            { value: "paid", label: "Đã thanh toán" },
            { value: "failed", label: "Thanh toán thất bại" },
            { value: "refunded", label: "Đã hoàn tiền" },
            { value: "partially_refunded", label: "Hoàn tiền một phần" },
          ],
        }),
      ],
    },
    {
      title: "Thông tin khách hàng",
      fields: [
        createInputSchemaItem({
          name: "customer.full_name",
          label: "Họ và tên",
          placeholder: "Nhập họ và tên khách hàng",
          required: true,
          validation: z.string().min(2, "Họ tên phải có ít nhất 2 ký tự"),
        }),
        createPhoneOrEmailSchemaItem({
          name: "customer.email",
          usingPhoneNumber: false,
          label: "Email",
          required: true,
        }),
        createPhoneOrEmailSchemaItem({
          name: "customer.phone_number",
          usingPhoneNumber: true,
          label: "Số điện thoại",
          required: true,
        }),
      ],
    },
    {
      title: "Địa chỉ giao hàng",
      fields: [
        createInputSchemaItem({
          name: "shipping_address.full_name",
          label: "Tên người nhận",
          placeholder: "Nhập tên người nhận",
          required: true,
        }),
        createPhoneOrEmailSchemaItem({
          name: "shipping_address.phone_number",
          usingPhoneNumber: true,
          label: "Số điện thoại người nhận",
          required: true,
        }),
        createInputSchemaItem({
          name: "shipping_address.address_line1",
          label: "Địa chỉ chi tiết",
          placeholder: "Số nhà, tên đường",
          required: true,
        }),
        createInputSchemaItem({
          name: "shipping_address.address_line2",
          label: "Địa chỉ bổ sung",
          placeholder: "Tòa nhà, căn hộ (tùy chọn)",
        }),
        createInputSchemaItem({
          name: "shipping_address.city",
          label: "Thành phố",
          placeholder: "Nhập thành phố",
          required: true,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "shipping_address.state",
          label: "Tỉnh/Thành",
          placeholder: "Nhập tỉnh/thành",
          required: true,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "shipping_address.postal_code",
          label: "Mã bưu điện",
          placeholder: "Nhập mã bưu điện",
          colSpan: 1,
        }),
        createSelectSchemaItem({
          name: "shipping_address.country",
          label: "Quốc gia",
          required: true,
          defaultValue: "VN",
          options: [
            { value: "VN", label: "Việt Nam" },
            { value: "US", label: "Hoa Kỳ" },
            { value: "JP", label: "Nhật Bản" },
            { value: "KR", label: "Hàn Quốc" },
          ],
          colSpan: 1,
        }),
      ],
    },
    {
      title: "Thông tin thanh toán",
      fields: [
        createSelectSchemaItem({
          name: "payment_method",
          label: "Phương thức thanh toán",
          required: true,
          options: [
            { value: "cod", label: "Thanh toán khi nhận hàng (COD)" },
            { value: "bank_transfer", label: "Chuyển khoản ngân hàng" },
            { value: "credit_card", label: "Thẻ tín dụng" },
            { value: "e_wallet", label: "Ví điện tử" },
            { value: "installment", label: "Trả góp" },
          ],
        }),
        createInputSchemaItem({
          name: "subtotal",
          type: "number",
          label: "Tổng tiền hàng (VND)",
          placeholder: "0",
          required: true,
          min: 0,
          disabled: true,
        }),
        createInputSchemaItem({
          name: "shipping_fee",
          type: "number",
          label: "Phí vận chuyển (VND)",
          placeholder: "0",
          min: 0,
          defaultValue: 0,
        }),
        createInputSchemaItem({
          name: "discount_amount",
          type: "number",
          label: "Giảm giá (VND)",
          placeholder: "0",
          min: 0,
          defaultValue: 0,
        }),
        createInputSchemaItem({
          name: "total_amount",
          type: "number",
          label: "Tổng thanh toán (VND)",
          placeholder: "0",
          required: true,
          min: 0,
          disabled: true,
        }),
      ],
    },
    {
      title: "Ghi chú",
      collapsible: true,
      defaultCollapsed: true,
      fields: [
        createTextareaSchemaItem({
          name: "notes",
          label: "Ghi chú đơn hàng",
          placeholder: "Nhập ghi chú cho đơn hàng (tùy chọn)",
          componentProps: { rows: 3 },
        }),
        createTextareaSchemaItem({
          name: "admin_notes",
          label: "Ghi chú nội bộ",
          placeholder: "Ghi chú dành cho admin (tùy chọn)",
          componentProps: { rows: 2 },
        }),
      ],
    },
  ];
};

/**
 * Seller Onboarding Schema (inspired by Medoo registration patterns)
 */
export const useSellerOnboardingSchema = () => {
  return (): FormSection[] => [
    {
      title: "Thông tin cá nhân",
      fields: [
        createInputSchemaItem({
          name: "personal_info.full_name",
          label: "Họ và tên",
          placeholder: "Nhập họ và tên đầy đủ",
          required: true,
          validation: z.string().min(2, "Họ tên phải có ít nhất 2 ký tự"),
        }),
        createPhoneOrEmailSchemaItem({
          name: "personal_info.email",
          usingPhoneNumber: false,
          label: "Email",
          required: true,
        }),
        createPhoneOrEmailSchemaItem({
          name: "personal_info.phone_number",
          usingPhoneNumber: true,
          label: "Số điện thoại",
          required: true,
        }),
        createInputSchemaItem({
          name: "personal_info.date_of_birth",
          type: "date",
          label: "Ngày sinh",
          required: true,
        }),
        createSelectSchemaItem({
          name: "personal_info.gender",
          label: "Giới tính",
          required: true,
          options: [
            { value: "male", label: "Nam" },
            { value: "female", label: "Nữ" },
            { value: "other", label: "Khác" },
          ],
        }),
        createInputSchemaItem({
          name: "personal_info.id_number",
          label: "Số CMND/CCCD",
          placeholder: "Nhập số CMND/CCCD",
          required: true,
          validation: z.string().min(9, "Số CMND/CCCD không hợp lệ"),
        }),
      ],
    },
    {
      title: "Thông tin cửa hàng",
      fields: [
        createInputSchemaItem({
          name: "store_info.store_name",
          label: "Tên cửa hàng",
          placeholder: "Nhập tên cửa hàng",
          required: true,
          validation: z.string().min(2, "Tên cửa hàng phải có ít nhất 2 ký tự"),
        }),
        createTextareaSchemaItem({
          name: "store_info.store_description",
          label: "Mô tả cửa hàng",
          placeholder: "Mô tả về cửa hàng của bạn",
          componentProps: { rows: 4 },
          validation: z.string().min(20, "Mô tả phải có ít nhất 20 ký tự"),
        }),
        createSelectSchemaItem({
          name: "store_info.business_type",
          label: "Loại hình kinh doanh",
          required: true,
          options: [
            { value: "individual", label: "Cá nhân" },
            { value: "company", label: "Công ty" },
            { value: "household", label: "Hộ kinh doanh" },
          ],
        }),
        createInputSchemaItem({
          name: "store_info.business_license",
          label: "Số giấy phép kinh doanh",
          placeholder: "Nhập số giấy phép (nếu có)",
          when: {
            field: "store_info.business_type",
            condition: (value) => value === "company",
          },
        }),
        createSelectSchemaItem({
          name: "store_info.main_category",
          label: "Ngành hàng chính",
          required: true,
          options: [
            { value: "electronics", label: "Điện tử" },
            { value: "fashion", label: "Thời trang" },
            { value: "home_garden", label: "Nhà cửa & Sân vườn" },
            { value: "beauty", label: "Làm đẹp" },
            { value: "sports", label: "Thể thao" },
            { value: "books", label: "Sách" },
            { value: "food", label: "Thực phẩm" },
            { value: "other", label: "Khác" },
          ],
        }),
      ],
    },
    {
      title: "Địa chỉ kinh doanh",
      fields: [
        createInputSchemaItem({
          name: "business_address.address_line1",
          label: "Địa chỉ chi tiết",
          placeholder: "Số nhà, tên đường",
          required: true,
        }),
        createInputSchemaItem({
          name: "business_address.address_line2",
          label: "Địa chỉ bổ sung",
          placeholder: "Tòa nhà, căn hộ (tùy chọn)",
        }),
        createInputSchemaItem({
          name: "business_address.ward",
          label: "Phường/Xã",
          placeholder: "Chọn phường/xã",
          required: true,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "business_address.district",
          label: "Quận/Huyện",
          placeholder: "Chọn quận/huyện",
          required: true,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "business_address.city",
          label: "Tỉnh/Thành phố",
          placeholder: "Chọn tỉnh/thành phố",
          required: true,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "business_address.postal_code",
          label: "Mã bưu điện",
          placeholder: "Nhập mã bưu điện",
          colSpan: 1,
        }),
      ],
    },
    {
      title: "Thông tin ngân hàng",
      fields: [
        createInputSchemaItem({
          name: "bank_info.account_holder_name",
          label: "Tên chủ tài khoản",
          placeholder: "Nhập tên chủ tài khoản",
          required: true,
        }),
        createInputSchemaItem({
          name: "bank_info.account_number",
          label: "Số tài khoản",
          placeholder: "Nhập số tài khoản",
          required: true,
          validation: z.string().min(6, "Số tài khoản không hợp lệ"),
        }),
        createSelectSchemaItem({
          name: "bank_info.bank_name",
          label: "Ngân hàng",
          required: true,
          options: [
            { value: "vietcombank", label: "Vietcombank" },
            { value: "techcombank", label: "Techcombank" },
            { value: "bidv", label: "BIDV" },
            { value: "vietinbank", label: "VietinBank" },
            { value: "agribank", label: "Agribank" },
            { value: "mbbank", label: "MB Bank" },
            { value: "acb", label: "ACB" },
            { value: "other", label: "Khác" },
          ],
        }),
        createInputSchemaItem({
          name: "bank_info.branch_name",
          label: "Chi nhánh",
          placeholder: "Nhập tên chi nhánh",
          required: true,
        }),
      ],
    },
    {
      title: "Tài liệu xác thực",
      fields: [
        createFileSchemaItem({
          name: "documents.id_front",
          label: "Ảnh CMND/CCCD mặt trước",
          accept: "image/*",
          required: true,
        }),
        createFileSchemaItem({
          name: "documents.id_back",
          label: "Ảnh CMND/CCCD mặt sau",
          accept: "image/*",
          required: true,
        }),
        createFileSchemaItem({
          name: "documents.business_license",
          label: "Giấy phép kinh doanh",
          accept: "image/*,application/pdf",
          when: {
            field: "store_info.business_type",
            condition: (value) => value === "company",
          },
        }),
        createFileSchemaItem({
          name: "documents.bank_statement",
          label: "Sao kê ngân hàng",
          accept: "image/*,application/pdf",
        }),
      ],
    },
    {
      title: "Điều khoản & Xác nhận",
      fields: [
        createCheckboxSchemaItem({
          name: "agreements.terms_of_service",
          label: "Tôi đồng ý với Điều khoản dịch vụ",
          required: true,
          validation: z.boolean().refine((val) => val === true, {
            message: "Bạn phải đồng ý với điều khoản dịch vụ",
          }),
        }),
        createCheckboxSchemaItem({
          name: "agreements.privacy_policy",
          label: "Tôi đồng ý với Chính sách bảo mật",
          required: true,
          validation: z.boolean().refine((val) => val === true, {
            message: "Bạn phải đồng ý với chính sách bảo mật",
          }),
        }),
        createCheckboxSchemaItem({
          name: "agreements.seller_policy",
          label: "Tôi đồng ý với Chính sách người bán",
          required: true,
          validation: z.boolean().refine((val) => val === true, {
            message: "Bạn phải đồng ý với chính sách người bán",
          }),
        }),
        createCheckboxSchemaItem({
          name: "agreements.data_accuracy",
          label: "Tôi xác nhận thông tin cung cấp là chính xác",
          required: true,
          validation: z.boolean().refine((val) => val === true, {
            message: "Bạn phải xác nhận tính chính xác của thông tin",
          }),
        }),
      ],
    },
  ];
};

/**
 * Product Review Schema (inspired by zen-buy.be review schema)
 */
export const useProductReviewSchema = () => {
  return (): FormSchemaItem[] => [
    createSelectSchemaItem({
      name: "rating",
      label: "Đánh giá",
      required: true,
      options: [
        { value: "5", label: "⭐⭐⭐⭐⭐ Rất tốt" },
        { value: "4", label: "⭐⭐⭐⭐ Tốt" },
        { value: "3", label: "⭐⭐⭐ Bình thường" },
        { value: "2", label: "⭐⭐ Không tốt" },
        { value: "1", label: "⭐ Rất tệ" },
      ],
      validation: z.string().min(1, "Vui lòng chọn đánh giá"),
    }),
    createInputSchemaItem({
      name: "title",
      label: "Tiêu đề đánh giá",
      placeholder: "Nhập tiêu đề cho đánh giá của bạn",
      validation: z.string().max(100, "Tiêu đề không được quá 100 ký tự").optional(),
    }),
    createTextareaSchemaItem({
      name: "content",
      label: "Nội dung đánh giá",
      placeholder: "Chia sẻ trải nghiệm của bạn về sản phẩm này",
      required: true,
      componentProps: { rows: 5 },
      validation: z.string().min(10, "Nội dung đánh giá phải có ít nhất 10 ký tự"),
    }),
    createFileSchemaItem({
      name: "images",
      label: "Hình ảnh đánh giá",
      accept: "image/*",
      multiple: true,
      maxFiles: 5,
      hint: "Thêm hình ảnh để đánh giá của bạn thêm sinh động (tối đa 5 ảnh)",
    }),
    createCheckboxSchemaItem({
      name: "is_anonymous",
      label: "Đánh giá ẩn danh",
      defaultValue: false,
    }),
  ];
};

/**
 * Checkout Form Schema
 */
export const useCheckoutFormSchema = () => {
  return (): FormSection[] => [
    {
      title: "Thông tin liên hệ",
      fields: [
        createPhoneOrEmailSchemaItem({
          name: "contact_email",
          usingPhoneNumber: false,
          label: "Email",
          required: true,
        }),
        createCheckboxSchemaItem({
          name: "subscribe_newsletter",
          label: "Đăng ký nhận tin khuyến mãi",
          defaultValue: false,
        }),
      ],
    },
    {
      title: "Địa chỉ giao hàng",
      fields: [
        createInputSchemaItem({
          name: "shipping.first_name",
          label: "Tên",
          placeholder: "Nhập tên",
          required: true,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "shipping.last_name",
          label: "Họ",
          placeholder: "Nhập họ",
          required: true,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "shipping.company",
          label: "Công ty",
          placeholder: "Nhập tên công ty (tùy chọn)",
        }),
        createInputSchemaItem({
          name: "shipping.address1",
          label: "Địa chỉ",
          placeholder: "Số nhà, tên đường",
          required: true,
        }),
        createInputSchemaItem({
          name: "shipping.address2",
          label: "Căn hộ, tòa nhà",
          placeholder: "Căn hộ, tòa nhà, v.v. (tùy chọn)",
        }),
        createInputSchemaItem({
          name: "shipping.city",
          label: "Thành phố",
          placeholder: "Nhập thành phố",
          required: true,
          colSpan: 1,
        }),
        createSelectSchemaItem({
          name: "shipping.province",
          label: "Tỉnh/Thành phố",
          required: true,
          options: [
            { value: "hanoi", label: "Hà Nội" },
            { value: "hcm", label: "TP. Hồ Chí Minh" },
            { value: "danang", label: "Đà Nẵng" },
            { value: "haiphong", label: "Hải Phòng" },
            { value: "cantho", label: "Cần Thơ" },
          ],
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "shipping.postal_code",
          label: "Mã bưu điện",
          placeholder: "Nhập mã bưu điện",
          colSpan: 1,
        }),
        createPhoneOrEmailSchemaItem({
          name: "shipping.phone",
          usingPhoneNumber: true,
          label: "Số điện thoại",
          required: true,
        }),
      ],
    },
    {
      title: "Phương thức vận chuyển",
      fields: [
        createSelectSchemaItem({
          name: "shipping_method",
          label: "Chọn phương thức vận chuyển",
          required: true,
          options: [
            { value: "standard", label: "Giao hàng tiêu chuẩn (3-5 ngày) - Miễn phí" },
            { value: "express", label: "Giao hàng nhanh (1-2 ngày) - 30,000 VND" },
            { value: "same_day", label: "Giao hàng trong ngày - 50,000 VND" },
          ],
        }),
      ],
    },
    {
      title: "Thanh toán",
      fields: [
        createSelectSchemaItem({
          name: "payment_method",
          label: "Phương thức thanh toán",
          required: true,
          options: [
            { value: "cod", label: "Thanh toán khi nhận hàng (COD)" },
            { value: "bank_transfer", label: "Chuyển khoản ngân hàng" },
            { value: "credit_card", label: "Thẻ tín dụng/Ghi nợ" },
            { value: "momo", label: "Ví MoMo" },
            { value: "zalopay", label: "ZaloPay" },
            { value: "vnpay", label: "VNPay" },
          ],
        }),
        createCheckboxSchemaItem({
          name: "billing_same_as_shipping",
          label: "Địa chỉ thanh toán giống địa chỉ giao hàng",
          defaultValue: true,
        }),
      ],
    },
    {
      title: "Ghi chú đơn hàng",
      collapsible: true,
      defaultCollapsed: true,
      fields: [
        createTextareaSchemaItem({
          name: "order_notes",
          label: "Ghi chú",
          placeholder: "Ghi chú về đơn hàng, ví dụ: ghi chú đặc biệt cho việc giao hàng",
          componentProps: { rows: 3 },
        }),
      ],
    },
  ];
};

/**
 * User Profile Update Schema
 */
export const useUserProfileSchema = () => {
  return (): FormSection[] => [
    {
      title: "Thông tin cá nhân",
      fields: [
        createInputSchemaItem({
          name: "first_name",
          label: "Tên",
          placeholder: "Nhập tên của bạn",
          required: true,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "last_name",
          label: "Họ",
          placeholder: "Nhập họ của bạn",
          required: true,
          colSpan: 1,
        }),
        createInputSchemaItem({
          name: "display_name",
          label: "Tên hiển thị",
          placeholder: "Nhập tên hiển thị",
        }),
        createPhoneOrEmailSchemaItem({
          name: "email",
          usingPhoneNumber: false,
          label: "Email",
          required: true,
          disabled: true,
          hint: "Email không thể thay đổi",
        }),
        createPhoneOrEmailSchemaItem({
          name: "phone_number",
          usingPhoneNumber: true,
          label: "Số điện thoại",
        }),
        createInputSchemaItem({
          name: "date_of_birth",
          type: "date",
          label: "Ngày sinh",
        }),
        createSelectSchemaItem({
          name: "gender",
          label: "Giới tính",
          options: [
            { value: "male", label: "Nam" },
            { value: "female", label: "Nữ" },
            { value: "other", label: "Khác" },
            { value: "prefer_not_to_say", label: "Không muốn tiết lộ" },
          ],
        }),
      ],
    },
    {
      title: "Ảnh đại diện",
      fields: [
        createFileSchemaItem({
          name: "avatar",
          label: "Ảnh đại diện",
          accept: "image/*",
          hint: "Chọn ảnh đại diện (tối đa 5MB)",
        }),
      ],
    },
    {
      title: "Tùy chọn",
      fields: [
        createCheckboxSchemaItem({
          name: "preferences.email_notifications",
          label: "Nhận thông báo qua email",
          defaultValue: true,
        }),
        createCheckboxSchemaItem({
          name: "preferences.sms_notifications",
          label: "Nhận thông báo qua SMS",
          defaultValue: false,
        }),
        createCheckboxSchemaItem({
          name: "preferences.marketing_emails",
          label: "Nhận email khuyến mãi",
          defaultValue: false,
        }),
        createSelectSchemaItem({
          name: "preferences.language",
          label: "Ngôn ngữ",
          defaultValue: "vi",
          options: [
            { value: "vi", label: "Tiếng Việt" },
            { value: "en", label: "English" },
          ],
        }),
      ],
    },
  ];
};

/**
 * Contact Form Schema
 */
export const useContactFormSchema = () => {
  return (): FormSchemaItem[] => [
    createInputSchemaItem({
      name: "name",
      label: "Họ và tên",
      placeholder: "Nhập họ và tên của bạn",
      required: true,
    }),
    createPhoneOrEmailSchemaItem({
      name: "email",
      usingPhoneNumber: false,
      label: "Email",
      required: true,
    }),
    createPhoneOrEmailSchemaItem({
      name: "phone",
      usingPhoneNumber: true,
      label: "Số điện thoại",
    }),
    createSelectSchemaItem({
      name: "subject",
      label: "Chủ đề",
      placeholder: "Chọn chủ đề",
      required: true,
      options: [
        { value: "general", label: "Câu hỏi chung" },
        { value: "support", label: "Hỗ trợ kỹ thuật" },
        { value: "billing", label: "Thanh toán" },
        { value: "partnership", label: "Hợp tác" },
        { value: "other", label: "Khác" },
      ],
    }),
    createTextareaSchemaItem({
      name: "message",
      label: "Tin nhắn",
      placeholder: "Nhập tin nhắn của bạn",
      required: true,
      componentProps: { rows: 5 },
      validation: z.string().min(10, "Tin nhắn phải có ít nhất 10 ký tự"),
    }),
  ];
};
