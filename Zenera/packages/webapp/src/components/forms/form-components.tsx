"use client";

import { forwardRef } from "react";
import { useController, Control, FieldPath, FieldValues } from "react-hook-form";
import { 
  FormField, 
  Input, 
  Textarea, 
  Select, 
  Checkbox, 
  RadioGroup, 
  FileInput,
  FormButton,
  type InputProps,
  type TextareaProps,
  type SelectProps,
  type CheckboxProps,
  type RadioGroupProps,
  type FileInputProps,
  type FormButtonProps,
} from "./form-field";
import { getErrorMessage } from "@/lib/forms/form-utils";

/**
 * React Hook Form Integrated Components
 * Components tích hợp với React Hook Form để tự động handle validation
 */

// Base Controller Props
interface BaseControllerProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  name: TName;
  control: Control<TFieldValues>;
  label?: string;
  hint?: string;
  required?: boolean;
  className?: string;
}

// Controlled Input
interface ControlledInputProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends BaseControllerProps<TFieldValues, TName>, 
  Omit<InputProps, 'name' | 'error'> {}

export function ControlledInput<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  control,
  label,
  hint,
  required,
  className,
  ...inputProps
}: ControlledInputProps<TFieldValues, TName>) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <FormField
      label={label}
      error={getErrorMessage(error)}
      hint={hint}
      required={required}
      className={className}
    >
      <Input
        {...field}
        {...inputProps}
        error={!!error}
      />
    </FormField>
  );
}

// Controlled Textarea
interface ControlledTextareaProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends BaseControllerProps<TFieldValues, TName>, 
  Omit<TextareaProps, 'name' | 'error'> {}

export function ControlledTextarea<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  control,
  label,
  hint,
  required,
  className,
  ...textareaProps
}: ControlledTextareaProps<TFieldValues, TName>) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <FormField
      label={label}
      error={getErrorMessage(error)}
      hint={hint}
      required={required}
      className={className}
    >
      <Textarea
        {...field}
        {...textareaProps}
        error={!!error}
      />
    </FormField>
  );
}

// Controlled Select
interface ControlledSelectProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends BaseControllerProps<TFieldValues, TName>, 
  Omit<SelectProps, 'name' | 'error'> {}

export function ControlledSelect<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  control,
  label,
  hint,
  required,
  className,
  ...selectProps
}: ControlledSelectProps<TFieldValues, TName>) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <FormField
      label={label}
      error={getErrorMessage(error)}
      hint={hint}
      required={required}
      className={className}
    >
      <Select
        {...field}
        {...selectProps}
        error={!!error}
      />
    </FormField>
  );
}

// Controlled Checkbox
interface ControlledCheckboxProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends BaseControllerProps<TFieldValues, TName>, 
  Omit<CheckboxProps, 'name' | 'error' | 'checked'> {}

export function ControlledCheckbox<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  control,
  label,
  hint,
  required,
  className,
  ...checkboxProps
}: ControlledCheckboxProps<TFieldValues, TName>) {
  const {
    field: { value, onChange, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <FormField
      error={getErrorMessage(error)}
      hint={hint}
      className={className}
    >
      <Checkbox
        {...field}
        {...checkboxProps}
        label={label}
        checked={!!value}
        onChange={(e) => onChange(e.target.checked)}
        error={!!error}
      />
    </FormField>
  );
}

// Controlled Radio Group
interface ControlledRadioGroupProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends BaseControllerProps<TFieldValues, TName>, 
  Omit<RadioGroupProps, 'name' | 'error' | 'value' | 'onChange'> {}

export function ControlledRadioGroup<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  control,
  label,
  hint,
  required,
  className,
  ...radioProps
}: ControlledRadioGroupProps<TFieldValues, TName>) {
  const {
    field: { value, onChange, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <FormField
      label={label}
      error={getErrorMessage(error)}
      hint={hint}
      required={required}
      className={className}
    >
      <RadioGroup
        {...field}
        {...radioProps}
        value={value}
        onChange={onChange}
        error={!!error}
      />
    </FormField>
  );
}

// Controlled File Input
interface ControlledFileInputProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends BaseControllerProps<TFieldValues, TName>, 
  Omit<FileInputProps, 'name' | 'error'> {}

export function ControlledFileInput<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  control,
  label,
  hint,
  required,
  className,
  ...fileInputProps
}: ControlledFileInputProps<TFieldValues, TName>) {
  const {
    field: { value, onChange, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <FormField
      label={label}
      error={getErrorMessage(error)}
      hint={hint}
      required={required}
      className={className}
    >
      <FileInput
        {...field}
        {...fileInputProps}
        onChange={(e) => {
          const files = e.target.files;
          if (fileInputProps.multiple) {
            onChange(files);
          } else {
            onChange(files?.[0] || null);
          }
        }}
        error={!!error}
      />
    </FormField>
  );
}

// Form Submit Button với loading state
interface FormSubmitButtonProps extends Omit<FormButtonProps, 'type'> {
  isSubmitting?: boolean;
  submittingText?: string;
}

export const FormSubmitButton = forwardRef<HTMLButtonElement, FormSubmitButtonProps>(
  ({ isSubmitting = false, submittingText = "Đang xử lý...", children, ...props }, ref) => {
    return (
      <FormButton
        {...props}
        type="submit"
        loading={isSubmitting}
        loadingText={submittingText}
        ref={ref}
      >
        {children}
      </FormButton>
    );
  }
);
FormSubmitButton.displayName = "FormSubmitButton";

// Form Reset Button
interface FormResetButtonProps extends Omit<FormButtonProps, 'type' | 'variant'> {
  onReset?: () => void;
}

export const FormResetButton = forwardRef<HTMLButtonElement, FormResetButtonProps>(
  ({ onReset, children = "Đặt lại", ...props }, ref) => {
    return (
      <FormButton
        {...props}
        type="button"
        variant="outline"
        onClick={onReset}
        ref={ref}
      >
        {children}
      </FormButton>
    );
  }
);
FormResetButton.displayName = "FormResetButton";
