"use client";

import i18next from "i18next";
import { Languages, DEFAULT_LOCALE } from "@/lib/constants/languages";

/**
 * Get translation function utility
 * Inspired by Medoo's getHTranslationFunc
 */
export const getZeneraTranslationFunc = (ns: string) => {
  const runsOnServerSide = typeof window === "undefined";

  if (runsOnServerSide) return {};
  
  const t = i18next.getFixedT(i18next.resolvedLanguage || DEFAULT_LOCALE, ns);

  return {
    t,
  };
};

/**
 * Get translation function for specific language
 */
export const getTranslationFuncForLanguage = (language: string, ns: string) => {
  const runsOnServerSide = typeof window === "undefined";

  if (runsOnServerSide) return {};
  
  const t = i18next.getFixedT(language, ns);

  return {
    t,
  };
};

/**
 * Check if translation key exists
 */
export const translationKeyExists = (key: string, ns: string = "translation", language?: string) => {
  const runsOnServerSide = typeof window === "undefined";

  if (runsOnServerSide) return false;
  
  const lng = language || i18next.resolvedLanguage || DEFAULT_LOCALE;
  return i18next.exists(key, { ns, lng });
};

/**
 * Get all translation keys for namespace
 */
export const getTranslationKeys = (ns: string = "translation", language?: string) => {
  const runsOnServerSide = typeof window === "undefined";

  if (runsOnServerSide) return [];
  
  const lng = language || i18next.resolvedLanguage || DEFAULT_LOCALE;
  const resources = i18next.getResourceBundle(lng, ns);
  
  if (!resources) return [];
  
  const keys: string[] = [];
  
  const extractKeys = (obj: any, prefix = "") => {
    Object.keys(obj).forEach(key => {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      if (typeof obj[key] === "object" && obj[key] !== null) {
        extractKeys(obj[key], fullKey);
      } else {
        keys.push(fullKey);
      }
    });
  };
  
  extractKeys(resources);
  return keys;
};

/**
 * Get translation with fallback
 */
export const getTranslationWithFallback = (
  key: string, 
  ns: string = "translation", 
  fallback?: string,
  language?: string
) => {
  const runsOnServerSide = typeof window === "undefined";

  if (runsOnServerSide) return fallback || key;
  
  const lng = language || i18next.resolvedLanguage || DEFAULT_LOCALE;
  const t = i18next.getFixedT(lng, ns);
  
  return t(key, fallback || key);
};

/**
 * Batch get translations
 */
export const getBatchTranslations = (
  keys: string[], 
  ns: string = "translation", 
  language?: string
): Record<string, string> => {
  const runsOnServerSide = typeof window === "undefined";

  if (runsOnServerSide) return {};
  
  const lng = language || i18next.resolvedLanguage || DEFAULT_LOCALE;
  const t = i18next.getFixedT(lng, ns);
  
  const result: Record<string, string> = {};
  keys.forEach(key => {
    result[key] = t(key, key);
  });
  
  return result;
};

/**
 * Export utilities object
 */
export const translationUtils = {
  getZeneraTranslationFunc,
  getTranslationFuncForLanguage,
  translationKeyExists,
  getTranslationKeys,
  getTranslationWithFallback,
  getBatchTranslations,
};
