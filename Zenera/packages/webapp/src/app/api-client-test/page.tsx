"use client";

import { useState } from 'react';
import { Button } from '@zenera/ui-components';
import { 
  useHealthCheck, 
  useLoadingState, 
  useErrorHandler,
  useCacheManager 
} from '@/lib/hooks/use-api';
import { 
  useAuthStatus, 
  useAuthActions,
  useLogin,
  useRegister 
} from '@/lib/hooks/use-auth-api';
import { 
  useProducts, 
  useCategories, 
  useFeaturedProducts,
  useProductActions 
} from '@/lib/hooks/use-products-api';

/**
 * API Client Test Page
 * Test TanStack Query integration và API functionality
 */
export default function ApiClientTestPage() {
  const [testResults, setTestResults] = useState<string[]>([]);
  
  // API Hooks
  const healthQuery = useHealthCheck();
  const { isAnyLoading } = useLoadingState();
  const { handleApiError } = useErrorHandler();
  const { clearCache } = useCacheManager();
  
  // Auth Hooks
  const { user, isAuthenticated, isLoading: authLoading } = useAuthStatus();
  const authActions = useAuthActions();
  
  // Products Hooks
  const productsQuery = useProducts({ limit: 5 });
  const categoriesQuery = useCategories();
  const featuredQuery = useFeaturedProducts(4);
  const productActions = useProductActions();

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testHealthCheck = async () => {
    try {
      addTestResult('Testing health check...');
      healthQuery.refetch();
      addTestResult('✅ Health check initiated');
    } catch (error) {
      addTestResult(`❌ Health check failed: ${error}`);
      handleApiError(error as Error);
    }
  };

  const testLogin = async () => {
    try {
      addTestResult('Testing login...');
      authActions.login({
        email: '<EMAIL>',
        password: 'password123'
      });
      addTestResult('✅ Login initiated');
    } catch (error) {
      addTestResult(`❌ Login failed: ${error}`);
      handleApiError(error as Error);
    }
  };

  const testRegister = async () => {
    try {
      addTestResult('Testing register...');
      authActions.register({
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Test',
        last_name: 'User',
        role: 'customer'
      });
      addTestResult('✅ Register initiated');
    } catch (error) {
      addTestResult(`❌ Register failed: ${error}`);
      handleApiError(error as Error);
    }
  };

  const testProducts = async () => {
    try {
      addTestResult('Testing products fetch...');
      productsQuery.refetch();
      addTestResult('✅ Products fetch initiated');
    } catch (error) {
      addTestResult(`❌ Products fetch failed: ${error}`);
      handleApiError(error as Error);
    }
  };

  const testCategories = async () => {
    try {
      addTestResult('Testing categories fetch...');
      categoriesQuery.refetch();
      addTestResult('✅ Categories fetch initiated');
    } catch (error) {
      addTestResult(`❌ Categories fetch failed: ${error}`);
      handleApiError(error as Error);
    }
  };

  const testClearCache = () => {
    clearCache();
    addTestResult('✅ Cache cleared');
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">API Client Test Page</h1>
      
      {/* Global Loading Indicator */}
      {isAnyLoading() && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
          🔄 API requests in progress...
        </div>
      )}

      {/* Auth Status */}
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-2">Authentication Status</h2>
        <div className="space-y-2">
          <p><strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}</p>
          <p><strong>Loading:</strong> {authLoading ? '🔄 Yes' : '✅ No'}</p>
          {user && (
            <div>
              <p><strong>User:</strong> {user.first_name} {user.last_name}</p>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Roles:</strong> {user.roles.join(', ')}</p>
            </div>
          )}
        </div>
      </div>

      {/* Test Buttons */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
        <Button 
          onClick={testHealthCheck}
          disabled={healthQuery.isFetching}
          variant="outline"
        >
          {healthQuery.isFetching ? '🔄' : '🏥'} Health Check
        </Button>

        <Button 
          onClick={testLogin}
          disabled={authActions.isLoggingIn}
          variant="outline"
        >
          {authActions.isLoggingIn ? '🔄' : '🔐'} Test Login
        </Button>

        <Button 
          onClick={testRegister}
          disabled={authActions.isRegistering}
          variant="outline"
        >
          {authActions.isRegistering ? '🔄' : '📝'} Test Register
        </Button>

        <Button 
          onClick={testProducts}
          disabled={productsQuery.isFetching}
          variant="outline"
        >
          {productsQuery.isFetching ? '🔄' : '📦'} Test Products
        </Button>

        <Button 
          onClick={testCategories}
          disabled={categoriesQuery.isFetching}
          variant="outline"
        >
          {categoriesQuery.isFetching ? '🔄' : '📂'} Test Categories
        </Button>

        <Button 
          onClick={testClearCache}
          variant="outline"
        >
          🗑️ Clear Cache
        </Button>
      </div>

      {/* API Results */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Health Check Results */}
        <div className="bg-white p-4 rounded-lg border">
          <h3 className="text-lg font-semibold mb-2">Health Check</h3>
          {healthQuery.data ? (
            <div className="text-green-600">
              <p>✅ Status: {healthQuery.data.status}</p>
              <p>📝 Message: {healthQuery.data.message}</p>
            </div>
          ) : healthQuery.error ? (
            <div className="text-red-600">
              <p>❌ Error: {healthQuery.error.message}</p>
            </div>
          ) : (
            <p className="text-gray-500">No data yet</p>
          )}
        </div>

        {/* Products Results */}
        <div className="bg-white p-4 rounded-lg border">
          <h3 className="text-lg font-semibold mb-2">Products</h3>
          {productsQuery.data ? (
            <div className="text-green-600">
              <p>✅ Found: {productsQuery.data.products.length} products</p>
              <p>📊 Total: {productsQuery.data.pagination.total}</p>
            </div>
          ) : productsQuery.error ? (
            <div className="text-red-600">
              <p>❌ Error: {productsQuery.error.message}</p>
            </div>
          ) : (
            <p className="text-gray-500">No data yet</p>
          )}
        </div>

        {/* Categories Results */}
        <div className="bg-white p-4 rounded-lg border">
          <h3 className="text-lg font-semibold mb-2">Categories</h3>
          {categoriesQuery.data ? (
            <div className="text-green-600">
              <p>✅ Found: {categoriesQuery.data.length} categories</p>
              <div className="mt-2">
                {categoriesQuery.data.slice(0, 3).map(cat => (
                  <p key={cat.id} className="text-sm">📂 {cat.name}</p>
                ))}
              </div>
            </div>
          ) : categoriesQuery.error ? (
            <div className="text-red-600">
              <p>❌ Error: {categoriesQuery.error.message}</p>
            </div>
          ) : (
            <p className="text-gray-500">No data yet</p>
          )}
        </div>

        {/* Featured Products Results */}
        <div className="bg-white p-4 rounded-lg border">
          <h3 className="text-lg font-semibold mb-2">Featured Products</h3>
          {featuredQuery.data ? (
            <div className="text-green-600">
              <p>✅ Found: {featuredQuery.data.length} featured products</p>
              <div className="mt-2">
                {featuredQuery.data.slice(0, 2).map(product => (
                  <p key={product.id} className="text-sm">⭐ {product.name}</p>
                ))}
              </div>
            </div>
          ) : featuredQuery.error ? (
            <div className="text-red-600">
              <p>❌ Error: {featuredQuery.error.message}</p>
            </div>
          ) : (
            <p className="text-gray-500">No data yet</p>
          )}
        </div>
      </div>

      {/* Test Results Log */}
      <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm">
        <h3 className="text-lg font-semibold mb-2 text-white">Test Results Log</h3>
        <div className="max-h-64 overflow-y-auto">
          {testResults.length === 0 ? (
            <p className="text-gray-500">No tests run yet...</p>
          ) : (
            testResults.map((result, index) => (
              <p key={index} className="mb-1">{result}</p>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
