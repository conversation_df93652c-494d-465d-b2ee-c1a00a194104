/**
 * Authentication Form Schemas
 * Zod schemas cho authentication forms
 */

import { z } from "zod";
import { commonValidations } from "../forms/form-utils";

/**
 * Login Schema
 */
export const loginSchema = z.object({
  email: commonValidations.email,
  password: z.string().min(1, "Mật khẩu là bắt buộc"),
  rememberMe: z.boolean().optional(),
});

export type LoginFormData = z.infer<typeof loginSchema>;

/**
 * Register Schema
 */
export const registerSchema = z.object({
  email: commonValidations.email,
  password: commonValidations.password,
  confirmPassword: z.string().min(1, "Xác nhận mật khẩu là bắt buộc"),
  firstName: commonValidations.required("Tên là bắt buộc"),
  lastName: commonValidations.required("<PERSON><PERSON> là bắt buộc"),
  username: z
    .string()
    .min(3, "<PERSON>ê<PERSON> đăng nhập phải có ít nhất 3 ký tự")
    .max(20, "Tên đăng nhập không được quá 20 ký tự")
    .regex(/^[a-zA-Z0-9_]+$/, "Tên đăng nhập chỉ được chứa chữ, số và dấu gạch dưới")
    .optional(),
  phoneNumber: commonValidations.phone.optional(),
  role: z.enum(["customer", "seller"], {
    required_error: "Vui lòng chọn loại tài khoản",
  }),
  agreeToTerms: z.boolean().refine((val) => val === true, {
    message: "Bạn phải đồng ý với điều khoản sử dụng",
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Mật khẩu xác nhận không khớp",
  path: ["confirmPassword"],
});

export type RegisterFormData = z.infer<typeof registerSchema>;

/**
 * Forgot Password Schema
 */
export const forgotPasswordSchema = z.object({
  email: commonValidations.email,
});

export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

/**
 * Reset Password Schema
 */
export const resetPasswordSchema = z.object({
  token: z.string().min(1, "Token là bắt buộc"),
  password: commonValidations.password,
  confirmPassword: z.string().min(1, "Xác nhận mật khẩu là bắt buộc"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Mật khẩu xác nhận không khớp",
  path: ["confirmPassword"],
});

export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

/**
 * Change Password Schema
 */
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Mật khẩu hiện tại là bắt buộc"),
  newPassword: commonValidations.password,
  confirmNewPassword: z.string().min(1, "Xác nhận mật khẩu mới là bắt buộc"),
}).refine((data) => data.newPassword === data.confirmNewPassword, {
  message: "Mật khẩu xác nhận không khớp",
  path: ["confirmNewPassword"],
}).refine((data) => data.currentPassword !== data.newPassword, {
  message: "Mật khẩu mới phải khác mật khẩu hiện tại",
  path: ["newPassword"],
});

export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;

/**
 * Profile Update Schema
 */
export const profileUpdateSchema = z.object({
  firstName: commonValidations.required("Tên là bắt buộc"),
  lastName: commonValidations.required("Họ là bắt buộc"),
  username: z
    .string()
    .min(3, "Tên đăng nhập phải có ít nhất 3 ký tự")
    .max(20, "Tên đăng nhập không được quá 20 ký tự")
    .regex(/^[a-zA-Z0-9_]+$/, "Tên đăng nhập chỉ được chứa chữ, số và dấu gạch dưới")
    .optional(),
  phoneNumber: commonValidations.phone.optional(),
  bio: z.string().max(500, "Mô tả không được quá 500 ký tự").optional(),
  avatar: z.instanceof(File).optional(),
});

export type ProfileUpdateFormData = z.infer<typeof profileUpdateSchema>;

/**
 * Address Schema
 */
export const addressSchema = z.object({
  id: z.string().optional(),
  type: z.enum(["home", "work", "other"], {
    required_error: "Vui lòng chọn loại địa chỉ",
  }),
  name: commonValidations.required("Tên địa chỉ là bắt buộc"),
  phone: commonValidations.phone,
  address: commonValidations.required("Địa chỉ là bắt buộc"),
  ward: commonValidations.required("Phường/Xã là bắt buộc"),
  district: commonValidations.required("Quận/Huyện là bắt buộc"),
  province: commonValidations.required("Tỉnh/Thành phố là bắt buộc"),
  postalCode: z.string().optional(),
  isDefault: z.boolean().optional(),
});

export type AddressFormData = z.infer<typeof addressSchema>;

/**
 * Seller Registration Schema
 */
export const sellerRegistrationSchema = z.object({
  // Personal Info
  firstName: commonValidations.required("Tên là bắt buộc"),
  lastName: commonValidations.required("Họ là bắt buộc"),
  email: commonValidations.email,
  phoneNumber: commonValidations.phone,
  
  // Business Info
  businessName: commonValidations.required("Tên doanh nghiệp là bắt buộc"),
  businessType: z.enum(["individual", "company"], {
    required_error: "Vui lòng chọn loại hình kinh doanh",
  }),
  taxId: z.string().optional(),
  businessLicense: z.instanceof(File).optional(),
  
  // Store Info
  storeName: commonValidations.required("Tên cửa hàng là bắt buộc"),
  storeDescription: z.string().max(1000, "Mô tả cửa hàng không được quá 1000 ký tự").optional(),
  storeCategory: commonValidations.required("Danh mục cửa hàng là bắt buộc"),
  
  // Address
  address: addressSchema,
  
  // Terms
  agreeToTerms: z.boolean().refine((val) => val === true, {
    message: "Bạn phải đồng ý với điều khoản bán hàng",
  }),
});

export type SellerRegistrationFormData = z.infer<typeof sellerRegistrationSchema>;

/**
 * Two-Factor Authentication Schema
 */
export const twoFactorSchema = z.object({
  code: z
    .string()
    .min(6, "Mã xác thực phải có 6 chữ số")
    .max(6, "Mã xác thực phải có 6 chữ số")
    .regex(/^\d{6}$/, "Mã xác thực chỉ được chứa số"),
});

export type TwoFactorFormData = z.infer<typeof twoFactorSchema>;

/**
 * Email Verification Schema
 */
export const emailVerificationSchema = z.object({
  code: z
    .string()
    .min(6, "Mã xác thực phải có 6 chữ số")
    .max(6, "Mã xác thực phải có 6 chữ số")
    .regex(/^\d{6}$/, "Mã xác thực chỉ được chứa số"),
});

export type EmailVerificationFormData = z.infer<typeof emailVerificationSchema>;
