"use client";

import { useEffect } from "react";
import { I18nextProvider } from "react-i18next";
import i18next from "@/lib/i18n/client";

/**
 * i18n Provider Component
 * Provides i18next instance to the app
 */
export function I18nProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Ensure i18next is initialized on client side
    if (typeof window !== "undefined" && !i18next.isInitialized) {
      console.log("Initializing i18next...");
    }
  }, []);

  return (
    <I18nextProvider i18n={i18next}>
      {children}
    </I18nextProvider>
  );
}
