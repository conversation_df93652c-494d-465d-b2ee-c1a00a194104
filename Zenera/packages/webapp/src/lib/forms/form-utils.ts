/**
 * Form Utilities
 * Utilities cho form handling với React Hook Form + Zod
 */

import { z } from "zod";
import { FieldError, FieldErrors } from "react-hook-form";

/**
 * Get error message từ field error
 */
export function getErrorMessage(error?: FieldError): string | undefined {
  if (!error) return undefined;
  return error.message;
}

/**
 * Get nested error message từ field path
 */
export function getNestedErrorMessage(
  errors: FieldErrors,
  path: string
): string | undefined {
  const keys = path.split('.');
  let current: any = errors;
  
  for (const key of keys) {
    if (current?.[key]) {
      current = current[key];
    } else {
      return undefined;
    }
  }
  
  return current?.message;
}

/**
 * Check if field has error
 */
export function hasError(errors: FieldErrors, path: string): boolean {
  return !!getNestedErrorMessage(errors, path);
}

/**
 * Format validation errors cho display
 */
export function formatValidationErrors(errors: z.ZodError): Record<string, string> {
  const formatted: Record<string, string> = {};
  
  errors.errors.forEach((error) => {
    const path = error.path.join('.');
    formatted[path] = error.message;
  });
  
  return formatted;
}

/**
 * Common validation schemas
 */
export const commonValidations = {
  email: z
    .string()
    .min(1, "Email là bắt buộc")
    .email("Email không hợp lệ"),
    
  password: z
    .string()
    .min(8, "Mật khẩu phải có ít nhất 8 ký tự")
    .regex(/[A-Z]/, "Mật khẩu phải có ít nhất 1 chữ hoa")
    .regex(/[a-z]/, "Mật khẩu phải có ít nhất 1 chữ thường")
    .regex(/[0-9]/, "Mật khẩu phải có ít nhất 1 số"),
    
  phone: z
    .string()
    .min(1, "Số điện thoại là bắt buộc")
    .regex(/^[0-9+\-\s()]+$/, "Số điện thoại không hợp lệ"),
    
  required: (message: string = "Trường này là bắt buộc") =>
    z.string().min(1, message),
    
  optional: z.string().optional(),
  
  url: z
    .string()
    .url("URL không hợp lệ")
    .optional()
    .or(z.literal("")),
    
  number: z
    .number()
    .min(0, "Số phải lớn hơn hoặc bằng 0"),
    
  positiveNumber: z
    .number()
    .min(1, "Số phải lớn hơn 0"),
    
  price: z
    .number()
    .min(0, "Giá phải lớn hơn hoặc bằng 0")
    .max(999999999, "Giá quá lớn"),
    
  slug: z
    .string()
    .min(1, "Slug là bắt buộc")
    .regex(/^[a-z0-9-]+$/, "Slug chỉ được chứa chữ thường, số và dấu gạch ngang"),
};

/**
 * Form field types
 */
export type FormFieldType = 
  | 'text'
  | 'email'
  | 'password'
  | 'number'
  | 'tel'
  | 'url'
  | 'textarea'
  | 'select'
  | 'checkbox'
  | 'radio'
  | 'file'
  | 'date'
  | 'datetime-local';

/**
 * Form field configuration
 */
export interface FormFieldConfig {
  name: string;
  label: string;
  type: FormFieldType;
  placeholder?: string;
  hint?: string;
  required?: boolean;
  options?: Array<{ value: string; label: string; disabled?: boolean }>;
  accept?: string; // for file inputs
  multiple?: boolean; // for file inputs
  min?: number; // for number inputs
  max?: number; // for number inputs
  step?: number; // for number inputs
}

/**
 * Generate form fields từ config
 */
export function generateFormFields(configs: FormFieldConfig[]) {
  return configs.reduce((acc, config) => {
    acc[config.name] = config;
    return acc;
  }, {} as Record<string, FormFieldConfig>);
}

/**
 * Validate file size
 */
export function validateFileSize(file: File, maxSizeMB: number = 5): boolean {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
}

/**
 * Validate file type
 */
export function validateFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type);
}

/**
 * Format file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Convert form data to FormData object
 */
export function toFormData(data: Record<string, any>): FormData {
  const formData = new FormData();
  
  Object.entries(data).forEach(([key, value]) => {
    if (value instanceof File) {
      formData.append(key, value);
    } else if (value instanceof FileList) {
      Array.from(value).forEach((file) => {
        formData.append(key, file);
      });
    } else if (Array.isArray(value)) {
      value.forEach((item) => {
        formData.append(`${key}[]`, item);
      });
    } else if (value !== null && value !== undefined) {
      formData.append(key, String(value));
    }
  });
  
  return formData;
}

/**
 * Debounce function cho form validation
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Form submission states
 */
export type FormSubmissionState = 'idle' | 'submitting' | 'success' | 'error';

/**
 * Form submission result
 */
export interface FormSubmissionResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  fieldErrors?: Record<string, string>;
}

/**
 * Handle form submission với error handling
 */
export async function handleFormSubmission<T>(
  submitFn: () => Promise<T>,
  onSuccess?: (data: T) => void,
  onError?: (error: string) => void
): Promise<FormSubmissionResult<T>> {
  try {
    const data = await submitFn();
    onSuccess?.(data);
    return { success: true, data };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Có lỗi xảy ra';
    onError?.(errorMessage);
    return { success: false, error: errorMessage };
  }
}
