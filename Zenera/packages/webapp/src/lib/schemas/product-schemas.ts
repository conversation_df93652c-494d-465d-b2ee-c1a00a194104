/**
 * Product Form Schemas
 * Zod schemas cho product management forms
 */

import { z } from "zod";
import { commonValidations } from "../forms/form-utils";

/**
 * Product Variant Schema
 */
export const productVariantSchema = z.object({
  id: z.string().optional(),
  name: commonValidations.required("Tên biến thể là bắt buộc"),
  sku: z.string().optional(),
  price: commonValidations.price,
  compareAtPrice: z.number().min(0).optional(),
  cost: z.number().min(0).optional(),
  inventory: z.number().min(0, "S<PERSON> lượng tồn kho phải lớn hơn hoặc bằng 0"),
  weight: z.number().min(0).optional(),
  dimensions: z.object({
    length: z.number().min(0).optional(),
    width: z.number().min(0).optional(),
    height: z.number().min(0).optional(),
  }).optional(),
  attributes: z.record(z.string()).optional(),
  images: z.array(z.string()).optional(),
  isActive: z.boolean().default(true),
});

export type ProductVariantFormData = z.infer<typeof productVariantSchema>;

/**
 * Product Image Schema
 */
export const productImageSchema = z.object({
  id: z.string().optional(),
  url: z.string().url("URL hình ảnh không hợp lệ"),
  alt: z.string().optional(),
  position: z.number().min(0).optional(),
  isMain: z.boolean().default(false),
});

export type ProductImageFormData = z.infer<typeof productImageSchema>;

/**
 * Product SEO Schema
 */
export const productSeoSchema = z.object({
  title: z.string().max(60, "Tiêu đề SEO không được quá 60 ký tự").optional(),
  description: z.string().max(160, "Mô tả SEO không được quá 160 ký tự").optional(),
  keywords: z.array(z.string()).optional(),
  slug: commonValidations.slug.optional(),
});

export type ProductSeoFormData = z.infer<typeof productSeoSchema>;

/**
 * Product Schema
 */
export const productSchema = z.object({
  // Basic Info
  name: commonValidations.required("Tên sản phẩm là bắt buộc"),
  description: z.string().max(5000, "Mô tả sản phẩm không được quá 5000 ký tự").optional(),
  shortDescription: z.string().max(500, "Mô tả ngắn không được quá 500 ký tự").optional(),
  
  // Pricing
  price: commonValidations.price,
  compareAtPrice: z.number().min(0).optional(),
  cost: z.number().min(0).optional(),
  
  // Inventory
  sku: z.string().optional(),
  inventory: z.number().min(0, "Số lượng tồn kho phải lớn hơn hoặc bằng 0"),
  trackInventory: z.boolean().default(true),
  allowBackorder: z.boolean().default(false),
  
  // Categories & Tags
  categoryId: commonValidations.required("Danh mục sản phẩm là bắt buộc"),
  subcategoryId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  brand: z.string().optional(),
  
  // Physical Properties
  weight: z.number().min(0).optional(),
  dimensions: z.object({
    length: z.number().min(0).optional(),
    width: z.number().min(0).optional(),
    height: z.number().min(0).optional(),
  }).optional(),
  
  // Images
  images: z.array(productImageSchema).optional(),
  mainImage: z.string().optional(),
  
  // Variants
  hasVariants: z.boolean().default(false),
  variants: z.array(productVariantSchema).optional(),
  
  // SEO
  seo: productSeoSchema.optional(),
  
  // Status
  status: z.enum(["draft", "active", "inactive"], {
    required_error: "Vui lòng chọn trạng thái sản phẩm",
  }),
  isDigital: z.boolean().default(false),
  isFeatured: z.boolean().default(false),
  
  // Shipping
  requiresShipping: z.boolean().default(true),
  shippingClass: z.string().optional(),
  
  // Additional Info
  attributes: z.record(z.string()).optional(),
  customFields: z.record(z.any()).optional(),
});

export type ProductFormData = z.infer<typeof productSchema>;

/**
 * Product Category Schema
 */
export const productCategorySchema = z.object({
  name: commonValidations.required("Tên danh mục là bắt buộc"),
  description: z.string().max(1000, "Mô tả danh mục không được quá 1000 ký tự").optional(),
  slug: commonValidations.slug,
  parentId: z.string().optional(),
  image: z.string().optional(),
  icon: z.string().optional(),
  isActive: z.boolean().default(true),
  sortOrder: z.number().min(0).optional(),
  seo: productSeoSchema.optional(),
});

export type ProductCategoryFormData = z.infer<typeof productCategorySchema>;

/**
 * Product Review Schema
 */
export const productReviewSchema = z.object({
  productId: commonValidations.required("ID sản phẩm là bắt buộc"),
  rating: z.number().min(1, "Đánh giá tối thiểu là 1 sao").max(5, "Đánh giá tối đa là 5 sao"),
  title: z.string().max(100, "Tiêu đề đánh giá không được quá 100 ký tự").optional(),
  comment: z.string().max(1000, "Bình luận không được quá 1000 ký tự").optional(),
  images: z.array(z.string()).optional(),
  isRecommended: z.boolean().optional(),
});

export type ProductReviewFormData = z.infer<typeof productReviewSchema>;

/**
 * Product Search Schema
 */
export const productSearchSchema = z.object({
  query: z.string().optional(),
  categoryId: z.string().optional(),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  brand: z.string().optional(),
  tags: z.array(z.string()).optional(),
  inStock: z.boolean().optional(),
  sortBy: z.enum([
    "name",
    "price_asc",
    "price_desc",
    "created_at",
    "updated_at",
    "popularity",
    "rating"
  ]).optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

export type ProductSearchFormData = z.infer<typeof productSearchSchema>;

/**
 * Bulk Product Update Schema
 */
export const bulkProductUpdateSchema = z.object({
  productIds: z.array(z.string()).min(1, "Vui lòng chọn ít nhất một sản phẩm"),
  action: z.enum([
    "update_status",
    "update_category",
    "update_price",
    "update_inventory",
    "delete"
  ]),
  data: z.record(z.any()).optional(),
});

export type BulkProductUpdateFormData = z.infer<typeof bulkProductUpdateSchema>;

/**
 * Product Import Schema
 */
export const productImportSchema = z.object({
  file: z.instanceof(File, { message: "Vui lòng chọn file để import" }),
  format: z.enum(["csv", "xlsx"], {
    required_error: "Vui lòng chọn định dạng file",
  }),
  updateExisting: z.boolean().default(false),
  skipErrors: z.boolean().default(false),
});

export type ProductImportFormData = z.infer<typeof productImportSchema>;

/**
 * Product Export Schema
 */
export const productExportSchema = z.object({
  format: z.enum(["csv", "xlsx"], {
    required_error: "Vui lòng chọn định dạng file",
  }),
  filters: productSearchSchema.optional(),
  fields: z.array(z.string()).optional(),
});

export type ProductExportFormData = z.infer<typeof productExportSchema>;
