import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Product, ProductDocument } from './schemas/product.schema';

@Injectable()
export class ProductsService {
  constructor(@InjectModel(Product.name) private productModel: Model<ProductDocument>) {}

  async create(createProductDto: any): Promise<Product> {
    const createdProduct = new this.productModel(createProductDto);
    return createdProduct.save();
  }

  async findAll(): Promise<Product[]> {
    const products = await this.productModel.find().exec();

    // If no products exist, return mock data for testing
    if (products.length === 0) {
      return this.getMockProducts();
    }

    return products;
  }

  private getMockProducts(): any[] {
    return [
      {
        name: 'iPhone 15 Pro',
        description: 'Latest iPhone with advanced features',
        price: 999,
        originalPrice: 1099,
        images: ['https://example.com/iphone.jpg'],
        category: 'Electronics',
        tags: ['phone', 'apple', 'smartphone'],
        inStock: true,
        stockQuantity: 50,
        rating: 4.8,
        reviewCount: 120,
        sellerId: 'seller1',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'MacBook Pro M3',
        description: 'Powerful laptop for professionals',
        price: 1999,
        originalPrice: 2199,
        images: ['https://example.com/macbook.jpg'],
        category: 'Electronics',
        tags: ['laptop', 'apple', 'computer'],
        inStock: true,
        stockQuantity: 25,
        rating: 4.9,
        reviewCount: 85,
        sellerId: 'seller1',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Nike Air Max',
        description: 'Comfortable running shoes',
        price: 129,
        originalPrice: 149,
        images: ['https://example.com/nike.jpg'],
        category: 'Fashion',
        tags: ['shoes', 'nike', 'running'],
        inStock: true,
        stockQuantity: 100,
        rating: 4.5,
        reviewCount: 200,
        sellerId: 'seller2',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
  }

  async findOne(id: string): Promise<Product> {
    return this.productModel.findById(id).exec();
  }

  async update(id: string, updateProductDto: any): Promise<Product> {
    return this.productModel
      .findByIdAndUpdate(id, updateProductDto, { new: true })
      .exec();
  }

  async remove(id: string): Promise<Product> {
    return this.productModel.findByIdAndDelete(id).exec();
  }

  async getCategories() {
    // Return mock categories for testing
    return [
      {
        id: '1',
        name: 'Electronics',
        slug: 'electronics',
        description: 'Electronic devices and gadgets',
        productCount: 2,
      },
      {
        id: '2',
        name: 'Fashion',
        slug: 'fashion',
        description: 'Clothing and accessories',
        productCount: 1,
      },
      {
        id: '3',
        name: 'Home & Garden',
        slug: 'home-garden',
        description: 'Home improvement and garden supplies',
        productCount: 0,
      },
    ];
  }
}
