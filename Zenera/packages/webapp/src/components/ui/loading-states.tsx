"use client";

import { cn } from "@/lib/utils";

/**
 * Loading States Components
 * Reusable loading indicators cho API operations
 */

// Basic Spinner
export function Spinner({ className, size = "md" }: { 
  className?: string; 
  size?: "sm" | "md" | "lg" 
}) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6", 
    lg: "w-8 h-8"
  };

  return (
    <div 
      className={cn(
        "animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",
        sizeClasses[size],
        className
      )}
    />
  );
}

// Loading Button
export function LoadingButton({ 
  children, 
  isLoading, 
  disabled,
  className,
  ...props 
}: {
  children: React.ReactNode;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
  [key: string]: any;
}) {
  return (
    <button
      disabled={disabled || isLoading}
      className={cn(
        "inline-flex items-center justify-center gap-2 px-4 py-2 rounded-md",
        "bg-blue-600 text-white hover:bg-blue-700",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        "transition-colors duration-200",
        className
      )}
      {...props}
    >
      {isLoading && <Spinner size="sm" className="border-white border-t-transparent" />}
      {children}
    </button>
  );
}

// Skeleton Loader
export function Skeleton({ className }: { className?: string }) {
  return (
    <div 
      className={cn(
        "animate-pulse bg-gray-200 rounded",
        className
      )}
    />
  );
}

// Product Card Skeleton
export function ProductCardSkeleton() {
  return (
    <div className="border rounded-lg p-4 space-y-4">
      <Skeleton className="h-48 w-full" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
        <Skeleton className="h-6 w-1/4" />
      </div>
    </div>
  );
}

// List Skeleton
export function ListSkeleton({ 
  count = 3, 
  itemHeight = "h-16" 
}: { 
  count?: number; 
  itemHeight?: string; 
}) {
  return (
    <div className="space-y-3">
      {Array.from({ length: count }).map((_, i) => (
        <Skeleton key={i} className={cn("w-full", itemHeight)} />
      ))}
    </div>
  );
}

// Page Loading Overlay
export function PageLoading({ message = "Loading..." }: { message?: string }) {
  return (
    <div className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="text-center">
        <Spinner size="lg" className="mx-auto mb-4" />
        <p className="text-gray-600">{message}</p>
      </div>
    </div>
  );
}

// Inline Loading
export function InlineLoading({ 
  message = "Loading...", 
  className 
}: { 
  message?: string; 
  className?: string; 
}) {
  return (
    <div className={cn("flex items-center justify-center gap-2 py-8", className)}>
      <Spinner size="sm" />
      <span className="text-gray-600">{message}</span>
    </div>
  );
}

// Error State
export function ErrorState({ 
  message = "Something went wrong", 
  onRetry,
  className 
}: { 
  message?: string; 
  onRetry?: () => void;
  className?: string; 
}) {
  return (
    <div className={cn("text-center py-8", className)}>
      <div className="text-red-500 text-4xl mb-4">⚠️</div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">Error</h3>
      <p className="text-gray-600 mb-4">{message}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
      )}
    </div>
  );
}

// Empty State
export function EmptyState({ 
  message = "No data found", 
  icon = "📭",
  action,
  className 
}: { 
  message?: string; 
  icon?: string;
  action?: React.ReactNode;
  className?: string; 
}) {
  return (
    <div className={cn("text-center py-12", className)}>
      <div className="text-6xl mb-4">{icon}</div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">No Data</h3>
      <p className="text-gray-600 mb-4">{message}</p>
      {action}
    </div>
  );
}

// Query State Handler
export function QueryStateHandler<T>({
  query,
  loading,
  error,
  empty,
  children
}: {
  query: {
    data?: T;
    isLoading?: boolean;
    error?: Error | null;
    refetch?: () => void;
  };
  loading?: React.ReactNode;
  error?: React.ReactNode;
  empty?: React.ReactNode;
  children: (data: T) => React.ReactNode;
}) {
  if (query.isLoading) {
    return <>{loading || <InlineLoading />}</>;
  }

  if (query.error) {
    return <>{error || <ErrorState onRetry={query.refetch} />}</>;
  }

  if (!query.data) {
    return <>{empty || <EmptyState />}</>;
  }

  return <>{children(query.data)}</>;
}

// API Status Indicator
export function ApiStatusIndicator({ 
  isOnline = true,
  className 
}: { 
  isOnline?: boolean;
  className?: string; 
}) {
  return (
    <div className={cn("flex items-center gap-2 text-sm", className)}>
      <div 
        className={cn(
          "w-2 h-2 rounded-full",
          isOnline ? "bg-green-500" : "bg-red-500"
        )}
      />
      <span className={isOnline ? "text-green-600" : "text-red-600"}>
        {isOnline ? "Online" : "Offline"}
      </span>
    </div>
  );
}
